<!DOCTYPE html>
<html>
<head>
    <title>Test Convert To Table Paste Widget</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-result { margin: 10px 0; padding: 10px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Convert To Table Paste Widget Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Array Structure Validation</h2>
        <div id="test1-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Menu Item Index Validation</h2>
        <div id="test2-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Operation Type Logic</h2>
        <div id="test3-result" class="test-result"></div>
    </div>

    <script>
        // Mock the required objects and constants for testing
        const mockData = {
            popOptionvalue: ["source_format", "destination_format","SEPARATOR", "T2T", "CONVERT_TO_TABLE", "ALL", "VALUES", "FORMATS", "FORMULAS", "COMMENTS", "VALUES_AND_NUMBERFORMATS", "FORMULAS_AND_NUMBERFORMATS", "ALL_EXCEPT_COMMENTS", "ALL_EXCEPT_BORDERS", "VALIDATION", "FILL_DEFAULT", "FILL_COPY", "FILL_FORMATS", "FILL_EXCEPT_FORMATS"],
            popOptionMsg: ["CopyPasteMatchWithSource", "CopyPasteMatchWithDestination","SEPARATOR","TexttoColumns.Title", "Table.Dialog.Create", "MenuItem.HomeTab.Paste.All", "MenuItem.HomeTab.Paste.Values", "MenuItem.HomeTab.Paste.Formats", "MenuItem.HomeTab.Paste.Formulas", "MenuItem.HomeTab.Paste.Notes", "MenuItem.HomeTab.Paste.ValuesAndNumberFormats", "MenuItem.HomeTab.Paste.FormulasAndNumberFormats", "MenuItem.HomeTab.Paste.AllExceptNotes", "MenuItem.HomeTab.Paste.AllExceptBorders", "MenuItem.HomeTab.Paste.Validation", "FillSeries.Default", "FillSeries.CopyCells", "FillSeries.FillFormats", "FillSeries.FillWithOutFormats"]
        };

        const ActionConstants = {
            SYSTEMCLIP_PASTE: 'SYSTEMCLIP_PASTE',
            SERVERCLIP_PASTE_RANGE: 'SERVERCLIP_PASTE_RANGE',
            COPY_PASTE: 'COPY_PASTE',
            FILLSERIES: 'FILLSERIES'
        };

        function runTest(testId, testName, testFunction) {
            const resultElement = document.getElementById(testId + '-result');
            try {
                const result = testFunction();
                if (result.success) {
                    resultElement.className = 'test-result pass';
                    resultElement.innerHTML = `<strong>PASS:</strong> ${result.message}`;
                } else {
                    resultElement.className = 'test-result fail';
                    resultElement.innerHTML = `<strong>FAIL:</strong> ${result.message}`;
                }
            } catch (error) {
                resultElement.className = 'test-result fail';
                resultElement.innerHTML = `<strong>ERROR:</strong> ${error.message}`;
            }
        }

        // Test 1: Validate array structure
        function test1() {
            const convertToTableIndex = mockData.popOptionvalue.indexOf('CONVERT_TO_TABLE');
            const expectedIndex = 4; // Should be at index 4
            
            if (convertToTableIndex === expectedIndex) {
                const correspondingMessage = mockData.popOptionMsg[convertToTableIndex];
                if (correspondingMessage === 'Table.Dialog.Create') {
                    return {
                        success: true,
                        message: `CONVERT_TO_TABLE found at correct index ${expectedIndex} with correct message key 'Table.Dialog.Create'`
                    };
                } else {
                    return {
                        success: false,
                        message: `CONVERT_TO_TABLE found at index ${expectedIndex} but has incorrect message: '${correspondingMessage}'`
                    };
                }
            } else {
                return {
                    success: false,
                    message: `CONVERT_TO_TABLE not found at expected index ${expectedIndex}. Found at: ${convertToTableIndex}`
                };
            }
        }

        // Test 2: Validate menu item index
        function test2() {
            const convertToTableIndex = mockData.popOptionvalue.indexOf('CONVERT_TO_TABLE');
            const expectedMenuItemId = 'menuItem4'; // Should correspond to menuItem4
            const actualMenuItemId = 'menuItem' + convertToTableIndex;
            
            if (actualMenuItemId === expectedMenuItemId) {
                return {
                    success: true,
                    message: `Menu item ID correctly maps to '${expectedMenuItemId}'`
                };
            } else {
                return {
                    success: false,
                    message: `Menu item ID mismatch. Expected: '${expectedMenuItemId}', Actual: '${actualMenuItemId}'`
                };
            }
        }

        // Test 3: Validate operation type logic
        function test3() {
            const convertToTableIndex = 4; // Index of CONVERT_TO_TABLE
            
            // Test copy-paste operations (should show Convert To Table)
            const copyPasteOperations = [
                ActionConstants.SYSTEMCLIP_PASTE,
                ActionConstants.SERVERCLIP_PASTE_RANGE,
                ActionConstants.COPY_PASTE
            ];
            
            // Test fill series operation (should NOT show Convert To Table)
            const fillSeriesOperation = ActionConstants.FILLSERIES;
            
            // Simulate the display logic
            function shouldShowConvertToTable(action) {
                switch (action) {
                    case ActionConstants.SYSTEMCLIP_PASTE:
                    case ActionConstants.SERVERCLIP_PASTE_RANGE:
                    case ActionConstants.COPY_PASTE:
                        return true; // Should show for copy-paste operations
                    case ActionConstants.FILLSERIES:
                        return false; // Should NOT show for fill series
                    default:
                        return false;
                }
            }
            
            let allTestsPassed = true;
            let messages = [];
            
            // Test copy-paste operations
            copyPasteOperations.forEach(operation => {
                if (shouldShowConvertToTable(operation)) {
                    messages.push(`✓ ${operation}: Correctly shows Convert To Table`);
                } else {
                    messages.push(`✗ ${operation}: Should show Convert To Table but doesn't`);
                    allTestsPassed = false;
                }
            });
            
            // Test fill series operation
            if (!shouldShowConvertToTable(fillSeriesOperation)) {
                messages.push(`✓ ${fillSeriesOperation}: Correctly hides Convert To Table`);
            } else {
                messages.push(`✗ ${fillSeriesOperation}: Should hide Convert To Table but doesn't`);
                allTestsPassed = false;
            }
            
            return {
                success: allTestsPassed,
                message: messages.join('<br>')
            };
        }

        // Run all tests
        document.addEventListener('DOMContentLoaded', function() {
            runTest('test1', 'Array Structure Validation', test1);
            runTest('test2', 'Menu Item Index Validation', test2);
            runTest('test3', 'Operation Type Logic', test3);
        });
    </script>
</body>
</html>
