/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:18 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home.landerincludes;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.adventnet.iam.xss.IAMEncoder;
import com.zoho.sheet.util.ClientUtils;
import com.adventnet.iam.security.SecurityRequestWrapper;

public final class defercss_jspf extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(4);
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityRequestWrapper");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


String stylePath = request.getParameter("stylePATH");
boolean isCompressed = Boolean.getBoolean("use.compression");
if(isCompressed){
String filePath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "sheetDefer.min.css");String integrityKeyPath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "sheetDefer.min.css", null, false);
      out.write("<script  type=\"text/javascript\" nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\">(function(ctx) {ctx.sPlusPlus.view.CSS.transient.DEFER_CSS = [\"");
      out.print(IAMEncoder.encodeJavaScript(filePath));
      out.write("\"];})(this);</script>\n");
} else { 
      out.write("<script  type=\"text/javascript\" nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\">(function(ctx) {ctx.sPlusPlus.view.CSS.transient.DEFER_CSS = [\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/formatPainter.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/extraIncludes.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/profileMenu.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/copyPaste.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/rangepicker.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/Feedback.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/textRotation.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/ZSheetDebugger.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/grouping.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPZia.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPZTranslation.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPOpenAi.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPConditionalFormatting.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPDefineName.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPSlicer.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPTimeline.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPZia2.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPTable.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPTheme.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPSpelling.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPDataCleaning.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/ReviewComments.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/Collaboration.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SparklineSidePanel.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPEditHistory.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/Picklist.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/Fields.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/functions.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/print.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/pivot.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/versionHistory.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/keyboardshortcut.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/charts.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/publish.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sharePublish.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/Border.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/CellNote.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/sPDataCleansing.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialog.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/sheetProperties.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/goto.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/texttocolumn.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/linkspreadsheet.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/dataconnection.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/template.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/signUp.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/datavalidation.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/auditDialog.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/LockCell.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/filter.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/namedrange.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/hyperlink.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/formatcell.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/goalseek.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/linkexternaldata.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/import.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/DataFromPicture.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/ShareDialog.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/solver.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/formPublish.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/Macro.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/dialogs/usersettings.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/CodeMirrorCustomFiles/codemirror.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/CodeMirrorCustomFiles/show-hint.css\",\"");
      out.print(IAMEncoder.encodeJavaScript(stylePath));
      out.write("/defer/sidepanel/SPColumnStats.css\",];})(this);</script>\n");
}
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
