/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:16 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home.landerincludes;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.adventnet.iam.xss.IAMEncoder;
import com.zoho.sheet.util.ClientUtils;

public final class StyleIncludes_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(3);
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


String cssPATH = request.getParameter("cssPATH");	//NO I18N 
boolean enableSRI = Boolean.parseBoolean(request.getParameter("enableSRI"));

 if(Boolean.getBoolean("use.apache")) { 
      out.write("<link rel=\"stylesheet\" type=\"text/css\" href='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "defaultFont.css"));
      out.write("'/>\n");
	if(enableSRI){
		String compressionExcludeIntegrityKeyPath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "compressionExclude.css", null, false); //NO I18N
		String sheetIconsIntegrityKeyPath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "spriteImages/sheetIcons.css", null, false); //NO I18N

      out.write("<link rel=\"stylesheet\" type=\"text/css\" href='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "compressionExclude.css"));
      out.write("' integrity=\"");
      out.print(ClientUtils.getIntegrity(compressionExcludeIntegrityKeyPath));
      out.write("\" crossorigin=\"anonymous\"/>\n");
      out.write("		<link rel=\"stylesheet\" type=\"text/css\" href='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "spriteImages/sheetIcons.css"));
      out.write("' integrity=\"");
      out.print(ClientUtils.getIntegrity(sheetIconsIntegrityKeyPath));
      out.write("\" crossorigin=\"anonymous\"/>\n");
  } else { 
      out.write("<link rel=\"stylesheet\" type=\"text/css\" href='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "compressionExclude.css"));
      out.write("'/>\n");
      out.write("		<link rel=\"stylesheet\" type=\"text/css\" href='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "spriteImages/sheetIcons.css"));
      out.write("'/>\n");
      out.write("	");
}
 } else { 
      out.write("<link rel=\"stylesheet\" type=\"text/css\" href=\"");
      out.print(IAMEncoder.encodeHTMLAttribute(cssPATH));
      out.write("defaultFont.min.css\"/>\n");
      out.write("	<link rel=\"stylesheet\" type=\"text/css\" href=\"");
      out.print(IAMEncoder.encodeHTMLAttribute(cssPATH));
      out.write("compressionExclude.css\"/>\n");
      out.write("	<link rel=\"stylesheet\" type=\"text/css\" href=\"");
      out.print(IAMEncoder.encodeHTMLAttribute(cssPATH));
      out.write("spriteImages/sheetIcons.css\"/>\n");
}
      String _jspx_temp0_url = "loadcss.jspf";
      String _jspx_temp0_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp0_url + ((_jspx_temp0_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("stylePATH", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp0_requestEncoding), out, false);
      String _jspx_temp1_url = "canvasloadcss.jspf";
      String _jspx_temp1_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp1_url + ((_jspx_temp1_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("stylePATH", _jspx_temp1_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp1_requestEncoding), out, false);
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
