/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:17 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home.landerincludes;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.adventnet.iam.xss.IAMEncoder;
import com.zoho.sheet.util.ClientUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.zoho.sheet.util.CurrentRealm;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import java.util.logging.Logger;
import java.util.logging.Level;
import com.zoho.sheet.authorization.DocIdentityBroadCaster;
import com.zoho.sheet.util.LocaleMsg;
import com.zoho.sheet.util.ClientUtils;
import com.adventnet.iam.security.SecurityRequestWrapper;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ClientUtils.ResourceType;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.iam.security.SecurityRequestWrapper;
import com.adventnet.iam.security.SecurityRequestWrapper;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;

public final class ScriptIncludes_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(4);
    _jspx_dependants.put("/sheet/appsheet/pages/home/<USER>/TransientResponse.jsp", Long.valueOf(1752654147000L));
    _jspx_dependants.put("/sheet/appsheet/pages/home/<USER>/WMSJSIncludes.jsp", Long.valueOf(1752654147000L));
    _jspx_dependants.put("/sheet/appsheet/LandingPage/jspf/QuartzIncludes.jsp", Long.valueOf(1752654147000L));
    _jspx_dependants.put("/sheet/appsheet/pages/home/<USER>/WMSHandler.jsp", Long.valueOf(1752654147000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(20);
    _jspx_imports_classes.add("com.zoho.sheet.util.CurrentRealm");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EnginePropertyUtil");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityRequestWrapper");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.Constants");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
    _jspx_imports_classes.add("java.util.logging.Logger");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.JSONObjectWrapper");
    _jspx_imports_classes.add("java.util.logging.Level");
    _jspx_imports_classes.add("com.zoho.sheet.authorization.DocIdentityBroadCaster");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils");
    _jspx_imports_classes.add("com.zoho.sheet.util.LocaleMsg");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils.ResourceType");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.LocaleUtil");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.UserProfile");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EngineConstants");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


String path = request.getParameter("path");	//NO I18N
String jsPath = request.getParameter("jsPATH");	//NO I18N
String jsMessagesPath = request.getParameter("jsMessagesPath");	//NO I18N 
String folder = request.getParameter("folder");
String delugepath = request.getParameter("delugepath");
String securityJSPath = request.getParameter("securityJSPath");
String writerXDCJsPath = request.getParameter("writerXDCJsPath");
boolean enableSRI = Boolean.valueOf(request.getParameter("enableSRI"));	

String initJSPath  = "../../../Views/"+folder+"/canvas_init.jspf";// No i18N
String gridJSPath  = "../../../Views/"+folder+"/canvas_grid.jspf";// No i18N
String chartthirdpartyJSPath  = "../../../Views/"+folder+"/chart_thirdparty.jspf";// No i18N
String chartsNewJSFilesPath  = "../../../Views/"+folder+"/charts_new_files.jspf";// No i18N
String chartsOldJSFilesPath  = "../../../Views/"+folder+"/charts_old_files.jspf";// No i18N
String chartsNewDeferJSFilesPath  = "../../../Views/"+folder+"/charts_new_defer_files.jspf";// No i18N
String chartsOldDeferJSFilesPath  = "../../../Views/"+folder+"/charts_old_defer_files.jspf";// No i18N
String defer1JSPath = "../../../Views/"+folder+"/canvas_defer1.jspf";// No i18N
String defer2JSPath = "../../../Views/"+folder+"/canvas_defer2.jspf";// No i18N

boolean isCompressed = Boolean.getBoolean("use.compression"); // No i18N
boolean isPreview = request.getAttribute("isPreview") != null ? (boolean) request.getAttribute("isPreview") : false;
Boolean IS_NIC_GRID = EngineConstants.IS_NIC_GRID;
boolean isThirdGenChartsEnabled = request.getAttribute("IS_THIRD_GEN_CHART_ENABLED") != null ? (boolean) request.getAttribute("IS_THIRD_GEN_CHART_ENABLED") : false;


	Logger logger=Logger.getLogger(this.getClass().getName());
	UserProfile uProfile = CurrentRealm.getUserProfile(); 
	String domainName = EnginePropertyUtil.getSheetPropertyValue("DomainName");	//No I18N
	String rebrandName = EnginePropertyUtil.getSheetPropertyValue("RebrandName"); //No I18N
	String country = LocaleUtil.getCountry();
	String langCode	= LocaleUtil.getLanguage();

	if(uProfile == null) {
		logger.log(Level.INFO,"[WMSHANDLER] User Profile is null, User Id >>  ",DocIdentityBroadCaster.getIdentity().getUserIdentity());
	}
		JSONObjectWrapper wmsOptions = ClientUtils.getWMSOptions(request);

      out.write("<script nonce=\"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" type=\"text/javascript\">(function (ctx) {(!ctx.sPlusPlus) && (ctx.sPlusPlus = {});var sPP = ctx.sPlusPlus;(!sPP.data.PageRegistry) && (sPP.data.PageRegistry = {}); sPP.data.PageRegistry.wmsOptions = ");
      out.print(wmsOptions);
      out.write("; })(this);</script>\n");
 if(!isPreview){ 

	String wms_js_path = ClientUtils.getDirectPath(ResourceType.WMS_JS, request);
	String wms_css_path = ClientUtils.getDirectPath(ResourceType.WMS_CSS, request); 

      out.write("<script crossorigin=\"anonymous\" type=\"text/javascript\" src=\"");
      out.print(wms_js_path);
      out.write("\"></script>	");
      out.write("<link rel=\"stylesheet\" type=\"text/css\" href=\"");
      out.print(wms_css_path);
      out.write('"');
      out.write('>');
      out.write('\n');
} 
      out.write("<script nonce=\"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" type=\"text/javascript\">(function(ctx) { ctx.sPlusPlus.transientResponse = {RESPONSE:");
      out.print(request.getAttribute("DOC_LOAD_RESPONSE"));
      out.write(" };})(this);</script>\n");
      out.write("<script type=\"text/javascript\" src = \"");
      out.print(IAMEncoder.encodeHTMLAttribute(jsMessagesPath));
      out.write("\"></script>\n");
      out.write("<script type=\"text/javascript\" src = \"");
      out.print(IAMEncoder.encodeHTMLAttribute(securityJSPath));
      out.write("security.min.js\"></script>\n");
      out.write("<script type=\"text/javascript\" src = \"");
      out.print(IAMEncoder.encodeHTMLAttribute(writerXDCJsPath));
      out.write("\"></script>\n");
      String _jspx_temp0_url = "ComponentsJSIncludes.jsp";
      String _jspx_temp0_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp0_url + ((_jspx_temp0_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp0_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("enableSRI", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( enableSRI ), _jspx_temp0_requestEncoding), out, false);
 if(!IS_NIC_GRID){ 
      out.write("<script  nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" type=\"text/javascript\">\n");
      out.write("	var d=document, s=d.createElement(\"div\");s.id=\"zquartztracker\";\n");
      out.write("	s.setAttribute(\"data-zqSettings\",'{\"consoleLogLevel\" : \"All\",\"sensitiveHeaders\" : \"\",\"sensitiveParams\" : \"\",\"response\":{\"collect\":false,\"maskSensitiveKeys\":[]} }');\n");
      out.write("	t=d.getElementsByTagName(\"script\")[0]; t.parentNode.insertBefore(s,t);\n");
      out.write("</script>\n");
      out.write("<script nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" src='");
      out.print(EnginePropertyUtil.getSheetPropertyValue("QUARTZ_URL"));
      out.write("' defer crossorigin=\"anonymous\"></script>\n");
      out.write("<script nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" src='");
      out.print(EnginePropertyUtil.getSheetPropertyValue("QUARTZ_REPORT_BUG_URL"));
      out.write("' defer crossorigin=\"anonymous\"></script>");
} 
 if(isCompressed){ 
	String filePath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.COMPONENT_JS, "zohocomponents.min.js"); //NO I18N
	if(enableSRI){
	String componentsIntegrityKeyPath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.COMPONENT_JS, "zohocomponents.min.js", null, false);//NO I18N

      out.write("<script type=\"text/javascript\" src=\"");
      out.print(IAMEncoder.encodeHTMLAttribute(filePath));
      out.write("\" integrity=\"");
      out.print(ClientUtils.getIntegrity(componentsIntegrityKeyPath));
      out.write("\" crossorigin=\"anonymous\"></script>\n");
}	else{
      out.write("<script type=\"text/javascript\" src=\"");
      out.print(IAMEncoder.encodeHTMLAttribute(filePath));
      out.write("\" ></script>\n");
}}else{
      out.write("<script type=\"text/javascript\" src=\"");
      out.print(IAMEncoder.encodeHTMLAttribute(jsPath));
      out.write("../zohocomponents/js/zohocomponents.js\"></script>\n");
}
      String _jspx_temp1_url =  initJSPath ;
      String _jspx_temp1_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp1_url + ((_jspx_temp1_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp1_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp1_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp1_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp1_requestEncoding), out, false);
      String _jspx_temp2_url =  gridJSPath ;
      String _jspx_temp2_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp2_url + ((_jspx_temp2_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp2_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp2_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp2_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp2_requestEncoding), out, false);
      String _jspx_temp3_url =  chartthirdpartyJSPath ;
      String _jspx_temp3_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp3_url + ((_jspx_temp3_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp3_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp3_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp3_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp3_requestEncoding), out, false);
 if (isThirdGenChartsEnabled) { 
      String _jspx_temp4_url =  chartsNewJSFilesPath ;
      String _jspx_temp4_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp4_url + ((_jspx_temp4_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp4_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp4_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp4_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp4_requestEncoding), out, false);
 } else { 
      String _jspx_temp5_url =  chartsOldJSFilesPath ;
      String _jspx_temp5_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp5_url + ((_jspx_temp5_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp5_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp5_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp5_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp5_requestEncoding), out, false);
 } 
      out.write("<!-- es6 modules import -->\n");
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, "./es6modules.jspf", out, false);
      String _jspx_temp6_url =  defer1JSPath ;
      String _jspx_temp6_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp6_url + ((_jspx_temp6_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp6_requestEncoding), out, false);
      String _jspx_temp7_url =  defer2JSPath ;
      String _jspx_temp7_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp7_url + ((_jspx_temp7_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp7_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp7_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp7_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp7_requestEncoding), out, false);
 if (isThirdGenChartsEnabled) { 
      String _jspx_temp8_url =  chartsNewDeferJSFilesPath ;
      String _jspx_temp8_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp8_url + ((_jspx_temp8_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp8_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp8_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp8_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp8_requestEncoding), out, false);
 } else { 
      String _jspx_temp9_url =  chartsOldDeferJSFilesPath ;
      String _jspx_temp9_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp9_url + ((_jspx_temp9_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp9_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPath ), _jspx_temp9_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp9_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp9_requestEncoding), out, false);
 } 
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
