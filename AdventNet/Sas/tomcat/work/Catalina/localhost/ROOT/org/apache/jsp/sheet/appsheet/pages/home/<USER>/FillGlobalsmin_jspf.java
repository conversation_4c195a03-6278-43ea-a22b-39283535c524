/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:17 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home.landerincludes;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.iam.IAMProxy;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.iam.UserAPI;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.zoho.sheet.util.CurrentRealm;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.security.ContentSecurityPolicy;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.util.ZSZFSNGUtils;
import com.adventnet.iam.security.SecurityUtil;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.Map;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.RemoteUtils.Keys;
import com.zoho.sheet.util.DynamicCDNUtils;
import com.adventnet.iam.security.SecurityRequestWrapper;
import com.zoho.sheet.authorization.util.AuthorizationUtil;

public final class FillGlobalsmin_jspf extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(32);
    _jspx_imports_classes.add("com.zoho.sheet.util.ZSZFSNGUtils");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.JSONConstants");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.ActionConstants");
    _jspx_imports_classes.add("com.zoho.sheet.util.CurrentRealm");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EnginePropertyUtil");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityUtil");
    _jspx_imports_classes.add("com.zoho.sheet.security.ContentSecurityPolicy");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.WorkbookContainer");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityRequestWrapper");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.Constants");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
    _jspx_imports_classes.add("java.util.logging.Logger");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.JSONObjectWrapper");
    _jspx_imports_classes.add("com.adventnet.iam.User");
    _jspx_imports_classes.add("java.util.logging.Level");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.DocumentUtils");
    _jspx_imports_classes.add("com.zoho.sheet.util.RemoteUtils.Keys");
    _jspx_imports_classes.add("com.zoho.sheet.util.RemoteUtils");
    _jspx_imports_classes.add("com.adventnet.iam.IAMProxy");
    _jspx_imports_classes.add("com.adventnet.iam.IAMUtil");
    _jspx_imports_classes.add("java.util.Map");
    _jspx_imports_classes.add("com.zoho.sheet.util.DynamicCDNUtils");
    _jspx_imports_classes.add("com.zoho.sheet.authorization.util.AuthorizationUtil");
    _jspx_imports_classes.add("com.adventnet.iam.UserAPI");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


Logger LOGGER 	= Logger.getLogger(this.getClass().getName());
String REMOTE_ENC_DOC_ID = IAMEncoder.encodeJavaScript(request.getParameter("doc"));
String REMOTE_BOOK_STATE_ID = null;
if(REMOTE_ENC_DOC_ID == null){
String doc = (String)request.getAttribute("doc");
REMOTE_ENC_DOC_ID = IAMEncoder.encodeJavaScript(doc);
try{
RemoteUtils.Keys remote_State_Id_Key = RemoteUtils.Keys.REMOTE_BOOK_STATE_ID;
Map<RemoteUtils.Keys, Object> remoteMetaData = RemoteUtils.getDecryptedContent(doc);
REMOTE_BOOK_STATE_ID = remoteMetaData.get(remote_State_Id_Key).toString();
}catch (Exception e){
LOGGER.log(Level.INFO, "Decrytpion failed while fetching RemoteBookState_Id");
}
}
String GADGET_URL        = EnginePropertyUtil.getSheetPropertyValue("ZohoGadgetsURL"); //No I18N
String QUARTZ_SERVER_URL = EnginePropertyUtil.getSheetPropertyValue("QUARTZ_SERVER_URL"); //No I18N
String QUARTZ_URL 		 = EnginePropertyUtil.getSheetPropertyValue("QUARTZ_URL"); //No I18N
String signInUpUrl       = Constants.rebrand_Properties.getProperty("SignInUpUrl"); //No I18N
String sheetServerURL    = request.getScheme() + "://" + EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL") + "/sheet/"; //No I18N
String OWNER_ID          = (request.getAttribute("OWNER_ID") != null) ? (String) request.getAttribute("OWNER_ID") : null;
Long OWNER_ZOID          = (request.getAttribute("OWNER_ZOID") != null) ? (Long) request.getAttribute("OWNER_ZOID") : null;
String ReportAbuseURL    = EnginePropertyUtil.getSheetPropertyValue("ReportAbuseURL"); //No I18N
String AboutZohoSheetURL = EnginePropertyUtil.getSheetPropertyValue("AboutZohoSheetURL"); //No I18N
String rebrandName       = Constants.rebrand_Properties.getProperty("serviceName"); //No I18N
String allowScalaExport  = EnginePropertyUtil.getSheetPropertyValue("enableHeadlessPDF"); //No I18N
String production_name   = EnginePropertyUtil.getSheetPropertyValue("PRODUCTION_SERVER_NAME"); //No I18N
Boolean isJSLogsNeeded   = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("isJSLogsNeeded")); //No I18N
int PRODUCT_TYPE         = (request.getAttribute("PRODUCT_TYPE") !=null) ? (Integer) request.getAttribute("PRODUCT_TYPE") : -1;
String fileFormat        = (String) request.getAttribute("pushFormat");
String remoteUserName    = (String) request.getAttribute("DISPLAYUSER");
String isCustomDomain    = (String) request.getAttribute("customDomains");
Boolean saveStatus    = (Boolean) request.getAttribute("saveStatus");
String CustomDomains    = (String) request.getAttribute("customDomainsList");
String ispostMessage    = (String) request.getAttribute("postMessageDomains");
String postMessageDomains    = (String) request.getAttribute("postMessageDomainsList");
String noLogo            = (String) request.getAttribute("nologo");
String logoUrl           = (String) request.getAttribute("logourl");
Boolean hideTopbar       = (Boolean) request.getAttribute("hidetop");
Boolean gridview       = (Boolean) request.getAttribute("gridview");
Boolean pausecollaboration       = (Boolean) request.getAttribute("pausecollaboration");
String hideMenuTab       = (String) request.getAttribute("hidemenubar");
String remoteModeConst   = (String) request.getAttribute("REMOTE_MODE");
Boolean noExport         = (Boolean) request.getAttribute("noExport");
Boolean isWhiteLabelled = (Boolean) request.getAttribute("isWhiteLabelled");
boolean isPreview        = request.getAttribute("isPreview") != null ? (boolean) request.getAttribute("isPreview") : false;
boolean isRangeGrid                  = request.getAttribute("isRangeGrid") != null ? (Boolean) request.getAttribute("isRangeGrid") : false;
boolean isSheetGrid                  = request.getAttribute("isSheetGrid") != null ? (Boolean) request.getAttribute("isSheetGrid") : false;
String versionNo          = (String) request.getAttribute("versionNo");
Double responseVersionNo = Double.parseDouble(EnginePropertyUtil.getSheetPropertyValue("WEB_CLIENT_RESPONSE_VERSION_NUMBER"));//No I18N
boolean passwordRemoteImport			= Boolean.parseBoolean(String.valueOf(request.getAttribute("isencrypted")));   //No I18N
String canShowBrandName = (String) request.getAttribute("canShowBrandName"); //No I18N
String brandName = (String) request.getAttribute("brand_name"); //No I18N
String webFontsCDN = (String) request.getAttribute("DYNAMIC_WEBFONTS_CDN");//NO I18N
String zohoFontsCDN = (String) request.getAttribute("DYNAMIC_ZOHOFONTS_CDN");//NO I18N
IAMProxy proxy = IAMProxy.getInstance();
UserAPI userAPI = proxy.getUserAPI();
long ZOID = Long.valueOf(DocumentUtils.getZOID());
long ZUID = Long.valueOf(DocumentUtils.getZUID());
User user = userAPI.getUserFromZUID(""+ZUID);
WorkbookContainer container = CurrentRealm.getContainer();
String resourceId = CurrentRealm.getContainerIdentity();
String docOwner = container.getDocOwnerFullName();
String orgName = "";
boolean is_user_wd_admin = (Boolean) request.getAttribute("IS_USER_WD_ADMIN");
if(!container.isRemoteMode() && !isRangeGrid && !isSheetGrid){
orgName = DocumentUtils.getZOrgName(resourceId);
if(orgName == null){
orgName = "";
}
}
String resourceOrgId = "-1";
if(!container.isRemoteMode()){
resourceOrgId = ZSZFSNGUtils.ZIODforRID(container.getResourceKey());
if(resourceOrgId == null){
resourceOrgId = "-1";
}
}
JSONObjectWrapper rebrandProp = Constants.rebrand_Urls;
String puviPath = EnginePropertyUtil.getSheetPropertyValue("ZOHO_PUVI_URL");//NO I18N
String puvifontServerUrl = zohoFontsCDN + puviPath;

      out.write("<script nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" type=\"text/javascript\">\n");
      out.write("(function(_){var e=_.sPlusPlus;var o=e.data.AppRegistry;const r=()=>{const _=o.SITE24x7_JS_URL;const e='");
      out.print(ZUID);
      out.write("';const r='");
      out.print(production_name);
      out.write("';if(window.performance&&window.performance.timing&&window.performance.navigation&&_){window.s247r=window.s247r||function(){(window.s247r.q=window.s247r.q||[]).push(arguments)};var a=document.createElement(\"script\");a.async=true;a.setAttribute(\"src\",_);document.getElementsByTagName(\"head\")[0].appendChild(a);window.s247r(\"environment\",r);window.s247r(\"userId\",e);window.s247r(\"trackAsynchronousCallbacks\",true);window.s247r(\"trackTransactionsWithQueryParams\",true);m=window.onerror,window.onerror=function(_,e,r,a,o){m&&m(_,e,r,a,o),o||(o=new Error(_)),(window.s247r.q=window.s247r.q||[]).push([\"captureException\",o])}}};r();var u={};(function(){var _='");
      out.print(REMOTE_ENC_DOC_ID != null ? REMOTE_ENC_DOC_ID : "");
      out.write("';var e=\"");
      out.print(IAMEncoder.encodeJavaScript(container.getResourceId()) != null ? IAMEncoder.encodeJavaScript(container.getResourceId()) : "");
      out.write("\";var r='");
      out.print(REMOTE_BOOK_STATE_ID != null ? IAMEncoder.encodeJavaScript(REMOTE_BOOK_STATE_ID ): "");
      out.write("';var a='");
      out.print(fileFormat != null ? fileFormat : "");
      out.write("';var o='");
      out.print(remoteUserName != null ?  IAMEncoder.encodeJavaScript(remoteUserName) : "");
      out.write("';var n=");
      out.print(isCustomDomain != null ? isCustomDomain : false);
      out.write(";var t=");
      out.print(saveStatus != null ? saveStatus : false);
      out.write(";var i=");
      out.print(noLogo != null ? noLogo  : false);
      out.write(";var s=");
      out.print(logoUrl);
      out.write(";var S=");
      out.print(hideTopbar != null ? hideTopbar : false);
      out.write(";var P=");
      out.print(hideMenuTab != null && hideMenuTab != "" ? hideMenuTab : false);
      out.write(";var J='");
      out.print(remoteModeConst != null ? remoteModeConst : "");
      out.write("';var d=");
      out.print(pausecollaboration != null ? pausecollaboration : false);
      out.write(";var w=");
      out.print(canShowBrandName != null ? canShowBrandName : false);
      out.write(";var c=");
      out.print(brandName != null ? brandName : null );
      out.write(";if(_){u.REMOTE_ENC_DOC_ID=_;u.RESOURCE_ID=e}r&&(u.REMOTE_BOOK_STATE_ID=r);a&&(u.fileFormat=a);o&&(u.remoteUserName=o);n&&(u.isCustomDomain=n);t&&(u.saveStatus=t);i&&(u.noLogo=i);s&&(u.logoUrl=s);S&&(u.hideTopbar=S);P&&(u.hideMenuTab=P);J&&(u.remoteModeConst=J);d&&(u.pauseCollaboration=d);w&&(u.canShowBrandName=w===\"true\");c&&(u.brandName=c)})();var a=this.location.href.split(\"authtoken=\")[1];var n={csrfParamName:\"");
      out.print(SecurityUtil.getCSRFParamName(request));
      out.write("\",csrfCookieName:\"");
      out.print(SecurityUtil.getCSRFCookieName(request));
      out.write("\",ctxpath:\"/sheet/\",accessIdentity:'");
      out.print(CurrentRealm.getAccessIdentity());
      out.write("',docCtxPath:'");
      out.print(IAMEncoder.encodeJavaScript(DocumentUtils.getDocumentContextPath(CurrentRealm.getAccessIdentity(),request)));
      out.write("',DOCUMENT_INFO:{DOCUMENT_POINTER:\"");
      out.print(CurrentRealm.getContainerIdentity());
      out.write("\"},authtoken:a,ZUID:'");
      out.print(ZUID);
      out.write("',loginName:'");
      out.print(user != null ? IAMEncoder.encodeJavaScript(user.getFullName()) : "");
      out.write("',loginEmailId:'");
      out.print(user != null ? IAMEncoder.encodeJavaScript(user.getPrimaryEmail()) : "");
      out.write("',rebrandProp:JSON.parse('");
      out.print(rebrandProp);
      out.write("'),isLoggedIn:");
      out.print(request.getUserPrincipal() != null ? true : false );
      out.write(",locale:\"");
      out.print(request.getAttribute("USER_LOCALE"));
      out.write("\",buildVersion:'");
      out.print(Constants.VERSION);
      out.write("',rID:\"");
      out.print(IAMEncoder.encodeJavaScript(container.getResourceKey()));
      out.write("\",ZOID:'");
      out.print(ZOID);
      out.write("',ORG_NAME:'");
      out.print(IAMEncoder.encodeJavaScript(orgName));
      out.write("',gadgetUrl:'");
      out.print(GADGET_URL);
      out.write("',quartzUrl:'");
      out.print(QUARTZ_URL);
      out.write("',quartzDomain:'");
      out.print(QUARTZ_SERVER_URL);
      out.write("',isLogsEnabled:");
      out.print(isJSLogsNeeded);
      out.write(",signInUpUrl:'");
      out.print(signInUpUrl);
      out.write("',sheetServerURL:'");
      out.print(sheetServerURL);
      out.write("',ZFSNG_PRODUCT_TYPE:'");
      out.print(PRODUCT_TYPE);
      out.write("',OWNER_ID:'");
      out.print(IAMEncoder.encodeJavaScript(OWNER_ID));
      out.write("',OWNER_ZOID:'");
      out.print(OWNER_ZOID);
      out.write("',isNewDoc:");
      out.print(container != null ? container.isNewDoc() : false);
      out.write(",isWhiteLabelled:");
      out.print(isWhiteLabelled != null ? isWhiteLabelled : false);
      out.write(",isPreview:");
      out.print(isPreview);
      out.write(",ReportAbuseURL:'");
      out.print(ReportAbuseURL);
      out.write("',viewCount:");
      out.print(request.getAttribute("viewCount"));
      out.write(",RMUSER:\"");
      out.print(request.getAttribute("ANONUSER"));
      out.write("\",NiceURL:\"");
      out.print(request.getAttribute(ActionConstants.NICEURL));
      out.write("\",authID:'");
      out.print(request.getAttribute("authId") != null  ? request.getAttribute("authId") :"" );
      out.write("',AboutZohoSheetURL:'");
      out.print(AboutZohoSheetURL);
      out.write("',rebrandName:'");
      out.print(rebrandName != null ? rebrandName : "");
      out.write("',collabInfo:'");
      out.print(request.getAttribute(Constants.COLLAB_ID));
      out.write("',CustomDomains:'");
      out.print(CustomDomains != null? CustomDomains : "");
      out.write("',ispostMessage:");
      out.print(ispostMessage != null ? ispostMessage : false);
      out.write(",postMessageDomains:'");
      out.print(postMessageDomains != null? postMessageDomains : "");
      out.write("',docID:'");
      out.print(container != null ? container.getDocId() : "");
      out.write("',rangeId:'");
      out.print(request.getAttribute(JSONConstants.RANGE_ID) != null ? request.getAttribute(JSONConstants.RANGE_ID).toString() : "");
      out.write("',docOwner:'");
      out.print(IAMEncoder.encodeJavaScript(docOwner));
      out.write("',puvifontServerUrl:'");
      out.print(puvifontServerUrl);
      out.write("',fontServerUrl:'");
      out.print(webFontsCDN);
      out.write("',passwordRemoteImport:");
      out.print(passwordRemoteImport);
      out.write(",RangeGridBoundary:_.isRangeGrid&&!_.isSheetGrid?_.RangeGridData:undefined,responseVersionNo:'");
      out.print(responseVersionNo);
      out.write("'};var t={ContentSecurityPolicy:\"");
      out.print(ContentSecurityPolicy.getPolicyWithoutUnsafeInline());
      out.write("\"};e.data.AppRegistry.LoadVar=n;e.data.AppRegistry.LoadVar.RemoteViewInfo=u;var i='");
      out.print(versionNo != null ? versionNo : "");
      out.write("';e.data.isVersionView=i!=null&&i!=\"\"?true:false;e.data.UserInfo.Permission.isDownloadOff=");
      out.print((noExport != null ? (noExport ?noExport : AuthorizationUtil.isCopyOff(request)): AuthorizationUtil.isCopyOff(request)));
      out.write(";e.data.AppRegistry.RESOURCE_ORG_ID=");
      out.print(resourceOrgId);
      out.write(";e.data.AppRegistry.CUSTOM_DOMAINS='");
      out.print(CustomDomains != null? CustomDomains : "");
      out.write("';e.data.AppRegistry.POSTMSG_DOMAINS='");
      out.print(postMessageDomains != null? postMessageDomains : "");
      out.write("';e.data.AppRegistry.isScalaExport=");
      out.print((allowScalaExport != null && "true".equals(allowScalaExport)? true : false));
      out.write(";if(e.data.UserInfo.Permission.isDownloadOff){window.addEventListener(\"contextmenu\",function(_){_.preventDefault()})}e.data.OrgInfo={};e.data.OrgInfo.is_user_wd_admin=");
      out.print(is_user_wd_admin);
      out.write("})(this);\n");
      out.write("</script>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
