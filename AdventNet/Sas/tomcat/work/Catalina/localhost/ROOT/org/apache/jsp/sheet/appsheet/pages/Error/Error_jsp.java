/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:47:10 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.Error;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import java.util.*;
import com.zoho.sheet.util.LocaleMsg;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ClientUtils.ResourceType;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;

public final class Error_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(6);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("java.util");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(7);
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EnginePropertyUtil");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils");
    _jspx_imports_classes.add("com.zoho.sheet.util.LocaleMsg");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.Constants");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils.ResourceType");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


	String listingPageUrl	= Constants.rebrand_Properties.getProperty("ListpageUrl");
	Calendar cal = new GregorianCalendar();
	int year = cal.get(Calendar.YEAR);
	String zohoSheetMail = EnginePropertyUtil.getSheetPropertyValue("HELP_MAIL_ID");   //No I18N

      out.write("<!DOCTYPE html>		");
      out.write("<html>\n");
      out.write("	<head>\n");
      out.write("		<link rel=\"stylesheet\" href='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "error.css"));
      out.write("'/>\n");
      out.write("		<link href=\"https://sites.zoho.com/webfonts?family=Open+Sans:400,600,800\" rel=\"stylesheet\">\n");
      out.write("		<link href = '");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.COMMON_IMAGE, Constants.rebrand_Properties.getProperty("favIconURLnew")));
      out.write("'  rel=\"SHORTCUT ICON\"/>\n");
      out.write("		\n");
      out.write("	</head>\n");
      out.write("	<body>\n");
      out.write("		<div class=\"errMain\">\n");
      out.write("			<div class=\"errHeader\">\n");
      out.write("				<!--  -->\n");
      out.write("				<a href=\"");
      out.print(listingPageUrl);
      out.write("\" class=\"errLogoAncor\"> <img class=\"errLogo\" src='");
      out.print(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.COMMON_IMAGE, "sheet_logo.svg"));
      out.write("' width=\"100px\"/></a>\n");
      out.write("				<ul class=\"blogs\">\n");
      out.write("					<li><a href='");
      out.print(Constants.rebrand_Properties.getProperty("BlogsURL",null));
      out.write("' target=\"_blank\" >");
      out.print(LocaleMsg.getMsg("LP.Blogs"));
      out.write("</a></li>	");
      out.write("<li><a href='");
      out.print(Constants.rebrand_Properties.getProperty("ForumsURL",null));
      out.write("' target=\"_blank\" >");
      out.print(LocaleMsg.getMsg("LP.Forums"));
      out.write("</a></li>	");
      out.write("</ul>\n");
      out.write("			</div>\n");
      out.write("			<div class=\"dspFlex flxGrow\">\n");
      out.write("				<ul class=\"qustCtn\">\n");
      out.write("					<li class=\"img-1\"></li>\n");
      out.write("					<li class=\"img-2\"></li>\n");
      out.write("					<li class=\"img-3\"></li>\n");
      out.write("					<li class=\"img-4\"></li>\n");
      out.write("					<li class=\"img-5\"></li>\n");
      out.write("					<li class=\"img-6\"></li>\n");
      out.write("					<li class=\"img-7\"></li>\n");
      out.write("					<li class=\"img-8\"></li>\n");
      out.write("					<li class=\"img-9\"></li>\n");
      out.write("					<li class=\"img-10\"></li>\n");
      out.write("					<li class=\"img-11\"></li>\n");
      out.write("					<li class=\"img-12\"></li>\n");
      out.write("					<li class=\"img-13\"></li>\n");
      out.write("					<li class=\"img-14\"></li>\n");
      out.write("					<li class=\"img-15\"></li>\n");
      out.write("					<li class=\"img-16\"></li>\n");
      out.write("					<li class=\"img-17\"></li>\n");
      out.write("					<li class=\"img-18\"></li>\n");
      out.write("					<li class=\"img-19\"></li>\n");
      out.write("					<li class=\"img-21\"></li>\n");
      out.write("					<li class=\"img-22\"></li>\n");
      out.write("					<li class=\"img-23\"></li>\n");
      out.write("				</ul>\n");
      out.write("				<section class=\"errCtn zoomIn\">	");
      out.write("<p class=\"sry\">");
      out.print(LocaleMsg.getMsg("ErrorMsg.couldnotProcessRequest"));
      out.write("</p>\n");
      out.write("					<ul class=\"sryTxt\">\n");
      out.write("						<li> ");
      out.print(LocaleMsg.getMsg("ErrorMsg.Reason.InvalidURL"));
      out.write("</li>\n");
      out.write("						<li> ");
      out.print(LocaleMsg.getMsg("ErrorMsg.Reason.RemovedFromLocation"));
      out.write("</li>\n");
      out.write("						<li> ");
      out.print(LocaleMsg.getMsg("ErrorMsg.Reason.NoAccess"));
      out.write("</li>\n");
      out.write("					</ul>\n");
      out.write("					<p>");
      out.print(LocaleMsg.getDynamicMsg("ErrorMsg.ProblemNotSolvedMsg", new String[]{"<a style='color:#1688e0;text-decoration:none' target='_blank' href='mailto:" + zohoSheetMail + "'>" + zohoSheetMail + "</a>"}));
      out.write("</p>\n");
      out.write("					<a href=\"");
      out.print(listingPageUrl);
      out.write("\">\n");
      out.write("						<button class=\"bcDocs\">");
      out.print(LocaleMsg.getMsg("ErrorPage.GoBackHome"));
      out.write("</button>\n");
      out.write("					</a>\n");
      out.write("				</section>	");
      out.write("</div>\n");
      out.write("			\n");
      out.write("		</div>\n");
      out.write("	</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
