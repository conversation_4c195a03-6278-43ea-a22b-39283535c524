/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:19 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home.landerincludes;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.iam.security.SecurityRequestWrapper;

public final class WebfontsIncludesmin_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(4);
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EnginePropertyUtil");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityRequestWrapper");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


String fontServerUrl =  (String) request.getAttribute("DYNAMIC_WEBFONTS_CDN");//NO I18N

      out.write("<style type=\"text/css\" nonce=\"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\">\n");
      out.write("@font-face {\n");
      out.write("font-family: 'lato';\n");
      out.write("font-weight: 400;\n");
      out.write("font-style: normal;\n");
      out.write("src: url(https://");
      out.print(fontServerUrl);
      out.write("/latoregular/font.eot);\n");
      out.write("src: url(https://");
      out.print(fontServerUrl);
      out.write("/latoregular/font.eot?#iefix) format(\"eot\"),\n");
      out.write("url(https://");
      out.print(fontServerUrl);
      out.write("/latoregular/font.woff2) format(\"woff2\"),\n");
      out.write("url(https://");
      out.print(fontServerUrl);
      out.write("/latoregular/font.woff) format(\"woff\")\n");
      out.write("}\n");
      out.write("@font-face {\n");
      out.write("font-family: 'lato';\n");
      out.write("font-weight: 600;\n");
      out.write("font-style: normal;\n");
      out.write("src: url(https://");
      out.print(fontServerUrl);
      out.write("/latobold/font.eot);\n");
      out.write("src: url(https://");
      out.print(fontServerUrl);
      out.write("/latobold/font.eot?#iefix) format(\"eot\"),\n");
      out.write("url(https://");
      out.print(fontServerUrl);
      out.write("/latobold/font.woff2) format(\"woff2\"),\n");
      out.write("url(https://");
      out.print(fontServerUrl);
      out.write("/latobold/font.woff) format(\"woff\")\n");
      out.write("}\n");
      out.write("@font-face {\n");
      out.write("font-family: 'Lato';\n");
      out.write("font-weight: 300;\n");
      out.write("font-style: normal;\n");
      out.write("src: url(https://");
      out.print(fontServerUrl);
      out.write("/latolight/font.eot);\n");
      out.write("src: url(https://");
      out.print(fontServerUrl);
      out.write("/latolight/font.eot?#iefix) format('eot'),\n");
      out.write("url(https://");
      out.print(fontServerUrl);
      out.write("/latolight/font.woff2) format('woff2'),\n");
      out.write("url(https://");
      out.print(fontServerUrl);
      out.write("/latolight/font.woff) format('woff')\n");
      out.write("}\n");
      out.write("</style>\n");
      out.write("<script nonce=\"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" >\n");
      out.write("(function(a){var t=a.sPlusPlus;var o=t.data.AppRegistry;var n=a.JSUtil;var r=n.FontUtils;var i=a.ZSGrid.Font;var s=a.ZSConstants;var e=s.FontConstants;if(!a.isRangeGrid){var d=t.data.DocRegistry;var l=t.data.AppRegistry.LoadVar.rID;var v=d[l].FontFamilyList;var F=[\"\",\"ZSTHEME_MINOR\",\"ZSTHEME_MAJOR\"];for(var u in v){var f=v[u].fontFamily;if(e.ZohoFonts.includes(f)){r.FontVariantLoader.loadZohoFont(f,true);continue}var c=r.checkisFontAvailable(f);if(c.isAvailable){r.FontVariantLoader.loadWebFonts([c.name])}else{if(!(F.includes(f)||f==undefined)){i.addInUnsupportedFonts(f)}}}}r.FontVariantLoader.loadWebFonts([\"Lato\",\"Roboto\"])})(this);\n");
      out.write("</script>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
