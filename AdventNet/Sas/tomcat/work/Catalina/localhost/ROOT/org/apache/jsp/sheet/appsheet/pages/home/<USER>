/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:16 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import java.util.Locale;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ClientUtils.ResourceType;
import com.zoho.sheet.util.CurrentRealm;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.sheet.util.LocaleMsg;
import com.adventnet.iam.IAMProxy;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.UserAPI;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.zoho.fcomponents.util.FComponentsUtil;
import com.zoho.sheet.security.ContentSecurityPolicy;
import java.util.logging.Logger;
import java.util.logging.Level;
import com.adventnet.iam.security.SecurityRequestWrapper;
import com.zoho.sheet.util.CurrentRealm;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.security.ContentSecurityPolicy;

public final class Lander_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/sheet/appsheet/pages/home/<USER>/JavaIncludes.jspf", Long.valueOf(1752654147000L));
    _jspx_dependants.put("/sheet/appsheet/pages/home/<USER>/OembedIncludes.jspf", Long.valueOf(1752654147000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(30);
    _jspx_imports_classes.add("com.zoho.fcomponents.util.FComponentsUtil");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.ActionConstants");
    _jspx_imports_classes.add("com.zoho.sheet.util.CurrentRealm");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EnginePropertyUtil");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityUtil");
    _jspx_imports_classes.add("com.zoho.sheet.security.ContentSecurityPolicy");
    _jspx_imports_classes.add("com.zoho.zfsng.client.ZohoFS");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.WorkbookContainer");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityRequestWrapper");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.Constants");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
    _jspx_imports_classes.add("java.util.logging.Logger");
    _jspx_imports_classes.add("com.adventnet.iam.User");
    _jspx_imports_classes.add("java.util.logging.Level");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.DocumentUtils");
    _jspx_imports_classes.add("java.util.Locale");
    _jspx_imports_classes.add("com.adventnet.iam.IAMProxy");
    _jspx_imports_classes.add("com.adventnet.iam.IAMUtil");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils");
    _jspx_imports_classes.add("com.zoho.sheet.util.LocaleMsg");
    _jspx_imports_classes.add("com.adventnet.iam.UserAPI");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils.ResourceType");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("<!DOCTYPE HTML>\n");
      out.write("<html>\n");
      out.write("	");

Logger logger=Logger.getLogger(this.getClass().getName());

String browser = request.getHeader("user-agent"); //No I18N
java.util.HashMap < String, Object > browserInfo = ClientUtils.extractWebBrowserInfo(browser);


String locale                        = (String) request.getAttribute("USER_LOCALE");
String path                          = "/sheet/appsheet"; //No I18N
String jsPATH                        = ClientUtils.getPath(ResourceType.APPSHEET_JS, request, true);
String cssPATH                       = ClientUtils.getPath(ResourceType.APPSHEET_CSS, request, true);
String appsheetImagePath             = ClientUtils.getPath(ResourceType.APPSHEET_IMAGE, request, true);
Boolean hideTopbar                   = (Boolean) request.getAttribute("hidetop");
Boolean hideFormulaBar               = (Boolean) request.getAttribute("hideFormulaBar");
Boolean hideSheetLogo                = (Boolean) request.getAttribute("hideSheetLogo");
boolean allowToWrite                 = request.getAttribute(Constants.ALLOW_TO_WRITE) != null ? (Boolean) request.getAttribute(Constants.ALLOW_TO_WRITE) : true;
String isDevMode                     = (String) request.getAttribute("IS_DEV_MODE");
String isDarkMode					 = request.getAttribute("DARKMODE") != null ? (String)request.getAttribute("DARKMODE") : "window.matchMedia('(prefers-color-scheme: dark)').matches";   //No I18N
String VIEW                          = (String) request.getAttribute(Constants.VIEW);
String folder                        = (String) request.getAttribute("FOLDER");
String delugePath                    = ClientUtils.getPath(ResourceType.DELUGE, request);
String securityJSPath                = ClientUtils.getPath(ResourceType.SECURITY_JS, request, true);
String UIDirection                   = ClientUtils.getUIDirection(request);

logger.log(Level.INFO,"SPREADSHEET PERMISSION:: VIEW::" + VIEW);

String[] localeAry = locale.split("_");
Locale myLocale = null;
int len = localeAry.length;
if(len > 1){
    myLocale = new Locale(localeAry[0], localeAry[1]);
} else {
    myLocale = new Locale(localeAry[0]);
}

String jsMessagesPath = ClientUtils.getAppsheetJSMessagePath(myLocale, request);
String menutabPath = ClientUtils.getMenutabPath(locale, folder);
String initHtmlPath = "../../html/Templates/Compressed/UITemplate_init_"+locale+ ("true".equals(isDevMode) ? ".jsp" : ".html");   //No I18N
String preinitHtmlPath = "../../html/Templates/Compressed/UITemplate_preinit_"+locale+ ("true".equals(isDevMode) ? ".jsp" : ".html");   //No I18N
String preHtmlPath = "../../html/Templates/Compressed/UITemplate_pre_"+locale+ ("true".equals(isDevMode) ? ".jsp" : ".html");   //No I18N

String bodyClass = "ui-zoho-sheet";	//No I18N
if("rtl".equals(UIDirection)) {
	bodyClass += " shRtl";		//No I18N
}

if(hideTopbar != null && hideTopbar){
	bodyClass += " noTop";		//No I18N
}

if("true".equals(isDarkMode)){
	bodyClass += " shDarkMode";	//No I18N
}

if(hideFormulaBar != null && hideFormulaBar){
	bodyClass += " noFormula";		//No I18N
}
if(hideSheetLogo != null && hideSheetLogo){
	bodyClass += " noSheetLogo";	//No I18N
}

String nonce = SecurityRequestWrapper.getInstance(request).getCSPNonce();
String existingCSP = response.getHeader("Content-Security-Policy");
if(existingCSP != null){
	response.setHeader("Content-Security-Policy", existingCSP +";"+ContentSecurityPolicy.getPolicyWithNonce(nonce)); //No I18N	
} else {
	response.setHeader("Content-Security-Policy", ContentSecurityPolicy.getPolicyWithNonce(nonce)); //No I18N
}


String SITES_WEBFONTS_URL = EnginePropertyUtil.getSheetPropertyValue("SITES_WEBFONTS_URL"); //No I18N
String SHEET_SERVER_URL   = EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL");		//No I18N
String WRITER_XDC_JS_PATH = EnginePropertyUtil.getSheetPropertyValue("WRITER_XDC_JS_PATH");		//No I18N
String WEBFONTS_STATIC_SERVER_URL =  (String) request.getAttribute("DYNAMIC_WEBFONTS_CDN");//NO I18N
String STATIC_PATH = (String) request.getAttribute("DYNAMIC_CDN");//NO I18N
String enableSRI   = EnginePropertyUtil.getSheetPropertyValue("ENABLE_SUBRESOURCE_INTEGRITY");		//No I18N

boolean isRangeGrid = request.getAttribute("isRangeGrid") != null ? (Boolean) request.getAttribute("isRangeGrid") : false;
boolean isCraftGrid = request.getAttribute("isCraftGrid") != null ? (Boolean) request.getAttribute("isCraftGrid") : false; 

String serviceWorkerPath       = (String) EnginePropertyUtil.getSheetPropertyValue("serviceWorkerPath"); // NO I18N
boolean isServiceWorkerEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("enableServiceWorker")); //NO i18N

String PRODUCT_NAME 				= EnginePropertyUtil.getSheetPropertyValue("PRODUCT_NAME");//No I18N



String _folder                        = (String) request.getAttribute("FOLDER");
String _SHEET_SERVER_URL              = EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL");	//No I18N
String _rID                           = CurrentRealm.getContainerIdentity();
String _oEmbedUrl                      = "https://"+_SHEET_SERVER_URL+"/sheet/open/"+_rID+"&format=json"; //No I18N
if("PUBLISHED".equals(_folder)){
	_oEmbedUrl =  "https://"+_SHEET_SERVER_URL+"/sheet/published.do?rid="+_rID+"&format=json"; //No I18N
}

      out.write("<head>	\n");
      out.write("		<title>");
      out.print(PRODUCT_NAME);
      out.write("</title>	");
      out.write("<meta name=\"viewport\" content=\"width=device-width\"/>\n");
      out.write("		<meta name=\"referrer\" content=\"no-referrer\" />\n");
      out.write("		<meta charset=\"UTF-8\">\n");
      out.write("		<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" />\n");
      out.write("		<meta name=\"referrer\" content=\"no-referrer\" />\n");
      out.write("		<meta name=\"robots\" content=\"noarchive, noindex, nofollow\">\n");
      out.write("		");
 if(Boolean.getBoolean("use.compression") && Boolean.getBoolean("use.apache")) { 
      out.write("<link rel=\"preconnect\" href=\"https://");
      out.print(STATIC_PATH);
      out.write("\" crossorigin>\n");
      out.write("		");
 } 
      out.write("<link rel=\"preconnect\" href=\"https://");
      out.print(WEBFONTS_STATIC_SERVER_URL);
      out.write("\" crossorigin>\n");
      out.write("		<link rel=\"alternate\" type=\"application/json+oembed\" href=\"http://");
      out.print(_SHEET_SERVER_URL);
      out.write("/sheet/services/oembed?url=");
      out.print(_oEmbedUrl);
      out.write("\" title=\"The spreadsheet software for collaborative teams\" />\n");
      out.write("		");
      out.write("<link href = '");
      out.print(ClientUtils.getFavIconURl(request));
      out.write("' rel=\"SHORTCUT ICON\"/>\n");
      out.write("\n");
      out.write("		<!-- It should be loaded before any resources  -->\n");
      out.write("        ");
      String _jspx_temp0_url = "landerincludes/ServiceWorkerIncludes.jsp";
      String _jspx_temp0_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp0_url + ((_jspx_temp0_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("nonce", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( nonce ), _jspx_temp0_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("serviceWorkerPath", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf(serviceWorkerPath), _jspx_temp0_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPATH ), _jspx_temp0_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("isServiceWorkerEnabled", _jspx_temp0_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf(isServiceWorkerEnabled), _jspx_temp0_requestEncoding), out, false);
      out.write("</head>\n");
      out.write("	<body class=\"");
      out.print(bodyClass);
      out.write("\" dir=\"");
      out.print(UIDirection);
      out.write("\">\n");
      out.write("\n");
      out.write("		");
 boolean isThirdGenChartsEnabled = request.getAttribute("IS_THIRD_GEN_CHART_ENABLED") != null ? (boolean) request.getAttribute("IS_THIRD_GEN_CHART_ENABLED") : false; 
      out.write("<div id=\"initGridLoader\" class=\"LdrArea\"></div>\n");
      out.write("\n");
      out.write("		");
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, preHtmlPath, out, false);
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, menutabPath, out, false);
      String _jspx_temp1_url = "landerincludes/ComponentsStyleIncludes.jsp";
      String _jspx_temp1_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp1_url + ((_jspx_temp1_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("cssPATH", _jspx_temp1_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp1_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("enableSRI", _jspx_temp1_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( enableSRI ), _jspx_temp1_requestEncoding), out, false);
      String _jspx_temp2_url = "landerincludes/StyleIncludes.jsp";
      String _jspx_temp2_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp2_url + ((_jspx_temp2_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("cssPATH", _jspx_temp2_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp2_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("enableSRI", _jspx_temp2_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( enableSRI ), _jspx_temp2_requestEncoding), out, false);
      String _jspx_temp3_url = "landerincludes/FillWindowGlobalsmin.jspf";
      String _jspx_temp3_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp3_url + ((_jspx_temp3_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("nonce", _jspx_temp3_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( nonce ), _jspx_temp3_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("enableSRI", _jspx_temp3_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( enableSRI ), _jspx_temp3_requestEncoding), out, false);
      String _jspx_temp4_url = "landerincludes/FillGlobalsmin.jspf";
      String _jspx_temp4_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp4_url + ((_jspx_temp4_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("nonce", _jspx_temp4_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( nonce ), _jspx_temp4_requestEncoding), out, false);
      String _jspx_temp5_url = "landerincludes/CustomDomainHandlermin.jspf";
      String _jspx_temp5_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp5_url + ((_jspx_temp5_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("nonce", _jspx_temp5_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( nonce ), _jspx_temp5_requestEncoding), out, false);
      String _jspx_temp6_url = "landerincludes/ScriptIncludes.jsp";
      String _jspx_temp6_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp6_url + ((_jspx_temp6_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("path", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( path ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("enableSRI", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( enableSRI ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsPATH", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( jsPATH ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("jsMessagesPath", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf(jsMessagesPath ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("securityJSPath", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( securityJSPath ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("folder", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( folder ), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("delugepath", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( delugePath), _jspx_temp6_requestEncoding) + "&" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("writerXDCJsPath", _jspx_temp6_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( WRITER_XDC_JS_PATH), _jspx_temp6_requestEncoding), out, false);
      String _jspx_temp7_url = "landerincludes/defercss.jspf";
      String _jspx_temp7_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp7_url + ((_jspx_temp7_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("stylePATH", _jspx_temp7_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp7_requestEncoding), out, false);
 if (isThirdGenChartsEnabled) { 
      String _jspx_temp8_url = "landerincludes/chartsNewDefer.jspf";
      String _jspx_temp8_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp8_url + ((_jspx_temp8_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("stylePATH", _jspx_temp8_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp8_requestEncoding), out, false);
 } else { 
      String _jspx_temp9_url = "landerincludes/chartsOldDefer.jspf";
      String _jspx_temp9_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp9_url + ((_jspx_temp9_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("stylePATH", _jspx_temp9_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf( cssPATH ), _jspx_temp9_requestEncoding), out, false);
 } 
      String _jspx_temp10_url = "landerincludes/WebfontsIncludesmin.jsp";
      String _jspx_temp10_requestEncoding = request.getCharacterEncoding();
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, _jspx_temp10_url + ((_jspx_temp10_url).indexOf('?')>0? '&': '?') + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode("sitesWebfontsURL", _jspx_temp10_requestEncoding)+ "=" + org.apache.jasper.runtime.JspRuntimeLibrary.URLEncode(String.valueOf(SITES_WEBFONTS_URL), _jspx_temp10_requestEncoding), out, false);
      out.write("</body>\n");
      out.write("</html> \n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
