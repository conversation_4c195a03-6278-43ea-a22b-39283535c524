/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: ATT
 * Generated at: 2025-07-18 06:59:16 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.sheet.appsheet.pages.home.landerincludes;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.iam.IAMProxy;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ClientUtils.ResourceType;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.zoho.sheet.util.LocaleMsg;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.zfsng.client.ZohoFS;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.iam.security.SecurityRequestWrapper;

public final class FillWindowGlobalsmin_jspf extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(16);
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.Utility");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EnginePropertyUtil");
    _jspx_imports_classes.add("com.adventnet.iam.IAMProxy");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils");
    _jspx_imports_classes.add("com.zoho.zfsng.client.ZohoFS");
    _jspx_imports_classes.add("com.zoho.sheet.util.LocaleMsg");
    _jspx_imports_classes.add("com.adventnet.iam.security.SecurityRequestWrapper");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.Constants");
    _jspx_imports_classes.add("com.zoho.sheet.util.ClientUtils.ResourceType");
    _jspx_imports_classes.add("com.adventnet.iam.xss.IAMEncoder");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.LocaleUtil");
    _jspx_imports_classes.add("com.adventnet.zoho.websheet.model.util.EngineConstants");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;


String ZOHO_ACCOUNTS_URL             = IAMProxy.getInstance().getIAMServerURL();
boolean enableSRI                         = Boolean.valueOf(request.getParameter("enableSRI"));	//NO I18N
boolean isPrintPreview				 = request.getAttribute("isPrintPreview")!= null ? (Boolean) request.getAttribute("isPrintPreview") : false;
boolean isRangeGrid                  = request.getAttribute("isRangeGrid") != null ? (Boolean) request.getAttribute("isRangeGrid") : false;
boolean isSheetGrid                  = request.getAttribute("isSheetGrid") != null ? (Boolean) request.getAttribute("isSheetGrid") : false;
boolean isCraftGrid                  = request.getAttribute("isCraftGrid") != null ? (Boolean) request.getAttribute("isCraftGrid") : false;
boolean isRCraftGrid			 = request.getAttribute("isRCraftGrid") != null ? (Boolean) request.getAttribute("isRCraftGrid") : false;//No I18N
boolean allowToWrite                 = request.getAttribute(Constants.ALLOW_TO_WRITE) != null ? (Boolean) request.getAttribute(Constants.ALLOW_TO_WRITE) : true;
boolean isCRMLibResource			 = request.getAttribute("isCRMLibResource") !=null ? (Boolean) request.getAttribute("isCRMLibResource") : false;
String isDevMode                     = (String) request.getAttribute("IS_DEV_MODE");
boolean iscdview 					 = request.getAttribute("cdview")!= null ? Boolean.parseBoolean((String)  request.getAttribute("cdview") ): false;//No I18N
int resourceType                     = request.getAttribute("RESOURCE_TYPE") != null ? (Integer) request.getAttribute("RESOURCE_TYPE") : -1;
String setup                         = EnginePropertyUtil.getSheetPropertyValue("SETUP");		//No I18N
String cssPATH                       = ClientUtils.getPath(ResourceType.APPSHEET_CSS, request, true);
String jsPATH                        = ClientUtils.getPath(ResourceType.APPSHEET_JS, request, true);
String appsheetImagePath             = ClientUtils.getPath(ResourceType.APPSHEET_IMAGE, request, true);
String functionsPath                 = ClientUtils.getFunctionsPath(LocaleUtil.getMyLocale(), request);
String delugePath                    = ClientUtils.getPath(ResourceType.DELUGE, request);
String delugeConnPath                = ClientUtils.getPath(ResourceType.DELUGE_CONNECTIONS, request);
String docsJSFilePath                = ClientUtils.getDirectPath(ResourceType.DOCS_JS, request);
String docsCSSFilePath               = ClientUtils.getDirectPath(ResourceType.DOCS_CSS, request);
String jsStaticServerURL             = request.getScheme() + "://" + EnginePropertyUtil.getSheetPropertyValue("JS_STATIC_SERVER_URL");
String cssStaticServerURL            = request.getScheme() + "://" + EnginePropertyUtil.getSheetPropertyValue("CSS_STATIC_SERVER_URL");
String wdJSFilePath 			 	 = ClientUtils.getDirectPath(ResourceType.WORKDRIVE_JS, request);
String docsVersion 					 = ZohoFS.getZDCStaticVersion("ZOHOSHEET");//No I18N
String fcomponentsCssPath            = ClientUtils.getPath(ResourceType.APPSHEET_FEATURE_COMPONENTS_CSS, request);
String wmsCssPath                    = ClientUtils.getDirectPath(ResourceType.WMS_CSS, request);
boolean isDarkModeEnabled            = ClientUtils.isFeatureEnabled("isDarkModeEnabled", "darkmodeEnablingOrgIds");	//No I18N
String hide_DC_Services		         = EnginePropertyUtil.getSheetPropertyValue("hide_DC_Services");	//No I18N
boolean isWebFontsEnabled		 	 = ClientUtils.isFeatureEnabled("isWebFontsEnabled", "all");	//No I18N
String LinkedDataLimitPerDoc		 = EnginePropertyUtil.getSheetPropertyValue("LinkedDataLimitPerDoc");	//No I18N
boolean isClientThumbnail			 = ClientUtils.isFeatureEnabled("clientThumbnail", null);	//No I18N
boolean isDataTemplateEnabled		 = ClientUtils.isFeatureEnabled("isDataTemplateEnabled", null);	//No I18N
String DOCS_SERVER_URL               = EnginePropertyUtil.getSheetPropertyValue("ZohoDocsURL");		//No I18N
String PUBLIC_SERVER_URL 	         = EnginePropertyUtil.getSheetPropertyValue("ZohoSheetPublicURL");//No I18N
String TABLES_URL 	                 = EnginePropertyUtil.getSheetPropertyValue("ZohoTablesURL");//No I18N
String CDN_SERVER_URL                = (String) request.getAttribute("DYNAMIC_CDN");//NO I18N
String FORM_SERVER_URL               = EnginePropertyUtil.getSheetPropertyValue("FormServerURL"); //No I18N
String FORMS_URL               		 = EnginePropertyUtil.getSheetPropertyValue("FormsURL"); //No I18N
String APPSTORE_URL                  = EnginePropertyUtil.getSheetPropertyValue("APPSTORE_URL"); //No I18N
String PLAYSTORE_URL                 = EnginePropertyUtil.getSheetPropertyValue("PLAYSTORE_URL"); //No I18N
String HUAWEI_URL                    = EnginePropertyUtil.getSheetPropertyValue("HUAWEI_URL"); //No I18N
String DOMAIN_ZOHO_URL               = EnginePropertyUtil.getSheetPropertyValue("DOMAIN_ZOHO_COM"); //No I18N
String COPY_SOURCE                   = EnginePropertyUtil.getSheetPropertyValue("CopySource"); //No I18N
int HTML_COPY_CELL_COUNT             = Integer.parseInt(EnginePropertyUtil.getSheetPropertyValue("HTML_COPY_CELL_COUNT")); //No I18N
int TSV_COPY_CELL_COUNT             = Integer.parseInt(EnginePropertyUtil.getEnginePropertyValue("VALUE_CHANGE_ACTION_CELLS_LIMIT")); //No I18N
long ZSHEET_PASTE_MAXLENGTH          = Long.parseLong(EnginePropertyUtil.getSheetPropertyValue("ZSHEET_PASTE_MAXLENGTH")); //No I18N
String NO_DOWNLOAD_COPY_SOURCE       = EnginePropertyUtil.getSheetPropertyValue("NO_DOWNLOAD_COPY_SOURCE"); //No I18N
String CHART_COPY_SOURCE             = EnginePropertyUtil.getSheetPropertyValue("CopyChartSource"); //No I18N
String RANGE_COPY_SOURCE             = EnginePropertyUtil.getSheetPropertyValue("CopyRangeSource"); //No I18N
String DOMAIN_SHEET_ZOHO_COM 	     = EnginePropertyUtil.getSheetPropertyValue("DOMAIN_SHEET_ZOHO_COM");//No I18N
String REMOTE_COPY_SOURCE	         = EnginePropertyUtil.getSheetPropertyValue("CopyRemoteSource");//No I18N
String isVerticalAnnouncement 		 = EnginePropertyUtil.getSheetPropertyValue("isVerticalAnnouncement");//No I18N
String VBA_PROGRAMMING               = EnginePropertyUtil.getSheetPropertyValue("VBA_PROGRAMMING"); //No I18N
String VBA_OBJECT_EXPLORER           = EnginePropertyUtil.getSheetPropertyValue("VBA_OBJECT_EXPLORER"); //No I18N
String SAMPLE_MACROS                 = EnginePropertyUtil.getSheetPropertyValue("SAMPLE_MACROS"); //No I18N
String USER_CONTRIBUTED_MACROS       = EnginePropertyUtil.getSheetPropertyValue("USER_CONTRIBUTED_MACROS"); //No I18N
String UNSUPPORTED_ACTIONS_IN_MACROS = EnginePropertyUtil.getSheetPropertyValue("UNSUPPORTED_ACTIONS_IN_MACROS"); //No I18N
String CRAFT_GRID_VALID_DOMAINS      = EnginePropertyUtil.getSheetPropertyValue("CRAFT_GRID_VALID_DOMAINS");		//No I18N
String ZiaDocsURL                    = EnginePropertyUtil.getSheetPropertyValue("ZiaDocsURL"); //NO I18N
String ZiaLearnMore                  = EnginePropertyUtil.getSheetPropertyValue("ZiaLearnMore"); //NO I18N
String isRangePicklistEnabled 		 = EnginePropertyUtil.getSheetPropertyValue("rangePicklistEnabled"); //No I18N
String showPasswordExportMenu        = EnginePropertyUtil.getSheetPropertyValue("showPasswordExportMenu");	//No I18N
boolean enableSaveToZohoTable         = ClientUtils.isFeatureEnabled("EnableSaveToZohoTable","SaveToZohoTableAllowedOrgIds"); //No I18N
String SITE24x7_JS_URL               = EnginePropertyUtil.getSheetPropertyValue("SITE24x7_JS_URL"); // no i18n
String w3ns1990xlink                 = EnginePropertyUtil.getSheetPropertyValue("W3_NS_1990_XLINK"); // no i18n
String w3ns2000svg                   = EnginePropertyUtil.getSheetPropertyValue("W3_NS_2000_SVG"); // no i18n
boolean isThemeEnabled                = ClientUtils.isFeatureEnabled("isThemeEnabled", "all"); //No I18N
boolean isOpenAIEnabled                = ClientUtils.isFeatureEnabled("IS_OPENAI_ENABLED", "OPENAIENABLED_ORG_ID"); //No I18N
String enableConnections             =  EnginePropertyUtil.getSheetPropertyValue("EnableConnections"); //No I18N
boolean isTableUIEnabled 			 = ClientUtils.isTableEnabled("isTableUIEnabled", "all"); //No I18N
boolean isCellEditHistoryEnabled     = ClientUtils.isFeatureEnabled("isCellEditHistoryEnabled", "all"); //No I18N
String Edit_History_Release_Date     = EnginePropertyUtil.getSheetPropertyValue("EditHistoryReleaseDate");	//No I18N
String Edit_History_Help_Url		 = EnginePropertyUtil.getSheetPropertyValue("editHistory_help_url");	//No I18N
String OpenAI_Help_Url		 		= EnginePropertyUtil.getSheetPropertyValue("openAIConfig_help_url");	//No I18N
int MAXNUMOFCOLS 					 = Utility.MAXNUMOFCOLS;
int MAXNUMOFROWS 					 = Utility.MAXNUMOFROWS;
String ZOHO_WORKDRIVE_URL		 	= EnginePropertyUtil.getSheetPropertyValue("DOMAIN_WORKDRIVE_ZOHO_COM");//No I18N
String PRODUCT_NAME 				= EnginePropertyUtil.getSheetPropertyValue("PRODUCT_NAME");//No I18N
String FEATURE_ZIA 				= EnginePropertyUtil.getSheetPropertyValue("FEATURE_ZIA");//No I18N
String PRODUCT_SHOW 				= EnginePropertyUtil.getSheetPropertyValue("PRODUCT_SHOW");//No I18N
String PRODUCT_WRITER 				= EnginePropertyUtil.getSheetPropertyValue("PRODUCT_WRITER");//No I18N
String PRODUCT_WORKDRIVE 				= EnginePropertyUtil.getSheetPropertyValue("PRODUCT_WORKDRIVE");//No I18N
String ZohoABTestingJSUrl            = Constants.rebrand_Properties.getProperty("ZohoABTestingJSUrl");
String USER_PHOTO_SERVER_URL         = ("true".equalsIgnoreCase(Constants.rebrand_Properties.getProperty("useAccountsAsWMSPhotoServer"))) ? IAMProxy.getInstance().getIAMServerURL() : IAMProxy.getInstance().getContactsServerURL(true); //NO I18N
Boolean IS_EXTERNAL_SHARE            = (request.getAttribute("IS_EXTERNAL_SHARE") !=null) ? (Boolean)request.getAttribute("IS_EXTERNAL_SHARE") : false;
String IS_TEAM_RESOURCE              = (String) request.getAttribute(Constants.IS_TEAM_RESOURCE);
boolean isWalkthroughViewed          = request.getAttribute("isWalkthroughViewed") != null ? (Boolean) request.getAttribute("isWalkthroughViewed") : true;
boolean isTourRippleViewed           = request.getAttribute("isTourRippleViewed") != null ? (Boolean) request.getAttribute("isTourRippleViewed") : true;
boolean isNewAnnouncementViewed      = request.getAttribute("isNewAnnouncementViewed") != null ? (Boolean) request.getAttribute("isNewAnnouncementViewed") : true;
boolean hidesheetdelete      = request.getAttribute("hidesheetdelete") != null ? (Boolean) request.getAttribute("hidesheetdelete") : false;
String displaymessage      = request.getAttribute("displaymessage") != null ? (String) request.getAttribute("displaymessage") : "true";
boolean isNewTagAccessed             = request.getAttribute("isNewTagAccessed") != null ? (Boolean) request.getAttribute("isNewTagAccessed") : true;
String isDarkMode	 			 = request.getAttribute("DARKMODE") != null ? (String)request.getAttribute("DARKMODE") : "false";   //No I18N
String isSysDefaultMode = "false";	//No I18N
if("sysDefault".equals(isDarkMode)){
isSysDefaultMode = "true";	//No I18N
isDarkMode = "window.matchMedia('(prefers-color-scheme: dark)').matches";	//No I18N
}
String spreadsheetFinalInfo			 = (String)request.getAttribute("FINAL_INFO");
String isCustomDomain                = (String) request.getAttribute("customDomains");
Boolean saveStatus                = (Boolean) request.getAttribute("saveStatus");
Boolean enableNewAnnouncement        = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("enableNewAnnouncement")); //No I18N
Boolean allowTourRipple              = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("allowTourRipple")); //No I18N
Boolean isOrgTemplatesEnabled              = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("isOrgTemplatesEnabled")); //No I18N
Boolean isNetworkCheckerEnabled 	= Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("enableNetworkChecker")); //NO i18N
String currentUserLocale 			 = LocaleUtil.getMyLocale().toString();
String FACEBOOK_SHARE_LINK 	 = EnginePropertyUtil.getSheetPropertyValue("FACEBOOK_SHARE_LINK");		//No I18N
String TWITTER_SHARE_LINK  	 = EnginePropertyUtil.getSheetPropertyValue("TWITTER_SHARE_LINK");		//No I18N
String LINKEDIN_SHARE_LINK 	 = EnginePropertyUtil.getSheetPropertyValue("LINKEDIN_SHARE_LINK");		//No I18N
String fileLocale					 =request.getAttribute("USER_LOCALE").toString();//No I18N
if(IS_TEAM_RESOURCE != null && "true".equals(IS_TEAM_RESOURCE)) {
DOCS_SERVER_URL = EnginePropertyUtil.getSheetPropertyValue("ZohoTeamDriveURL");	//No I18N
}
if(IS_EXTERNAL_SHARE) {
DOCS_SERVER_URL = EnginePropertyUtil.getSheetPropertyValue("ZohoSheetPublicURL");	//No I18N
}
Boolean WORKFLOW_EXIST = (request.getAttribute("WORKFLOW_EXIST") != null) ? (Boolean)request.getAttribute("WORKFLOW_EXIST") : false;
String folder = (String) request.getAttribute("FOLDER");
String viewFolderPath = "Views/" + folder + "/"; // no i18n
boolean isCompression = Boolean.getBoolean("use.compression");	//NO I18N
boolean isStatic = Boolean.getBoolean("use.apache");			//NO I18N
String workerPath = "/sheet/appsheet/Views/" + folder + "/worker.js"; //No I18N
if(isCompression) {
String sPath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_JS, "worker.min.js", folder); //No I18N
workerPath = "/sheet/appsheet/js/zsheetworkermin.js?jsPath="+sPath+"&maxRow="+MAXNUMOFROWS+"&maxCol="+MAXNUMOFCOLS;  // No i18N
}
String ALLOWED_FILE_SIZE = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_IMPORT_FILE_SIZE_IN_MB_NEW");	//No I18N
String ALLOWED_FILE_FORMATS = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_DEFAULT_FOERMATS");	//No I18N
String ZOHO_SHEET_URL 			 = EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL");//No I18N
String adminPanelURL 			= EnginePropertyUtil.getSheetPropertyValue("adminPanelURL");//No I18N
String userGuideURL 				= EnginePropertyUtil.getSheetPropertyValue("userGuideURL");//No I18N
String communityURL 				= EnginePropertyUtil.getSheetPropertyValue("communityURL");//No I18N
String blogsURL 					= EnginePropertyUtil.getSheetPropertyValue("blogsURL");//No I18N
String whatsNewURL 					= EnginePropertyUtil.getSheetPropertyValue("whatsNewURL");//No I18N
String webinarsURL 					= EnginePropertyUtil.getSheetPropertyValue("webinarsURL");//No I18N
String apiHelpPageURL 				= EnginePropertyUtil.getSheetPropertyValue("apiHelpPageURL");//No I18N
String tipsAndTricks_1_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_1_URL");//No I18N
String tipsAndTricks_2_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_2_URL");//No I18N
String tipsAndTricks_3_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_3_URL");//No I18N
String tipsAndTricks_4_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_4_URL");//No I18N
String tipsAndTricks_5_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_5_URL");//No I18N
String tipsAndTricks_6_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_6_URL");//No I18N
String tipsAndTricks_7_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_7_URL");//No I18N
String tipsAndTricks_8_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_8_URL");//No I18N
String tipsAndTricks_9_URL 			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_9_URL");//No I18N
String tipsAndTricks_10_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_10_URL");//No I18N
String tipsAndTricks_11_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_11_URL");//No I18N
String tipsAndTricks_12_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_12_URL");//No I18N
String tipsAndTricks_13_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_13_URL");//No I18N
String tipsAndTricks_14_URL			= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_14_URL");//No I18N
String tipsAndTricks_15_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_15_URL");//No I18N
String tipsAndTricks_16_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_16_URL");//No I18N
String tipsAndTricks_17_URL 		= EnginePropertyUtil.getSheetPropertyValue("tipsAndTricks_17_URL");//No I18N
String moreTipsURL 					= EnginePropertyUtil.getSheetPropertyValue("moreTipsURL");//No I18N
String migrateToWorkDrive           = EnginePropertyUtil.getSheetPropertyValue("migrateToWorkDrive"); //No I18N
String ChromeExtensionURL 		 	= EnginePropertyUtil.getSheetPropertyValue("ChromeExtensionURL");//No I18N
String FirefoxExtensionURL 		 	= EnginePropertyUtil.getSheetPropertyValue("FirefoxExtensionURL");//No I18N
String EdgeExtensionURL 		 	= EnginePropertyUtil.getSheetPropertyValue("EdgeExtensionURL");//No I18N
String ListingUrl					= Constants.rebrand_Properties.getProperty("ListpageUrl");//No I18N
boolean isIterativeCalcEnabled 		 	= Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("isIterativeCalcEnabled"));   //No I18N
Boolean isMergeFieldEnabled			= ClientUtils.isFeatureEnabled("isMergeFieldEnabled", "all");   //No I18N
Boolean defaultReadUser 			=  request.getAttribute(Constants.DEFAULT_READ_USER) != null ? (Boolean) request.getAttribute(Constants.DEFAULT_READ_USER) : false;
Boolean isZillumPlan 				=  request.getAttribute(Constants.IS_ZILLUM_PLAN) != null ? (Boolean) request.getAttribute(Constants.IS_ZILLUM_PLAN) : false;
Boolean isWDPersonalEdition 		=  request.getAttribute(Constants.IS_WD_PERSONAL_EDITION) != null ? (Boolean) request.getAttribute(Constants.IS_WD_PERSONAL_EDITION) : false;
Boolean overrideBrowserShortcut	 	= request.getAttribute("OVERRIDE_BROWSER_SHORTCUT") != null ? (Boolean) request.getAttribute("OVERRIDE_BROWSER_SHORTCUT") : true;
Boolean isPublicTemplatesEnabled	= Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("isPublicTemplateEnabled")); //No I18N
String VIEW                         = (String) request.getAttribute(Constants.VIEW);
Boolean IS_NIC_GRID 				= EngineConstants.IS_NIC_GRID;
String NIC_DISABLED_FUNCTION_LIST   = EnginePropertyUtil.getSheetPropertyValue("NIC_DISABLED_FUNCTION_LIST");		//No I18N
boolean isNonZohoUser	 			= request.getAttribute("is_non_zoho_user") != null ? (Boolean) request.getAttribute("is_non_zoho_user") : false;
Boolean ENABLE_MAIL_NOTIFICATION_SETTINGS   = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("ENABLE_MAIL_NOTIFICATION_SETTINGS")); //No I18N
String writerRteFilePath				= EnginePropertyUtil.getSheetPropertyValue("WRITER_RTE_PATH");//No I18N
Boolean enableXlsmExport			= Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("CONVERSION_EXPORT_XLSM"));		//No I18N
Boolean ENABLE_UPLOADAPI_IMPORT   = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("ENABLE_UPLOADAPI_IMPORT")); //No I18N
Boolean isinputboxsupported			= Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("isinputboxsupported"));		//No I18N
int NR_MAX_ROWS = Integer.parseInt(EnginePropertyUtil.getEnginePropertyValue("MAX_NO_OF_ROWS")); //No I18N
int NR_MAX_COLS = Integer.parseInt(EnginePropertyUtil.getEnginePropertyValue("MAX_NO_OF_COLS")); //No I18N
int MAX_TRANSLATION_LENGTH = Integer.parseInt(EnginePropertyUtil.getSheetPropertyValue("MAX_TRANSLATION_DATA_LIMIT")); //No I18N
Boolean isOcrCSEZ = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("OCR_CSEZ_MODE")); 	//No I18N
Boolean isOcrEnabled = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("IS_OCR_ENABLED")); 	//No I18N
Boolean translationEnabled = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("IS_TRANSLATION_ENABLED")); 	//No I18N
String UPLOAD_SERVER_URL   = EnginePropertyUtil.getSheetPropertyValue("UPLOAD_SERVER_URL"); //No I18N
Boolean isPatternFillEnabled = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("isPatternFillEnabled")); 	//No I18N
String WD_ORG_ID      = request.getAttribute("WD_ORG_ID") != null ? (String) request.getAttribute("WD_ORG_ID") : "";
Boolean isColumnStatsEnabled = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("COLUMN_STATS_ENABLED")); 	//No I18N

      out.write("<script nonce = \"");
      out.print(SecurityRequestWrapper.getInstance(request).getCSPNonce());
      out.write("\" type=\"text/javascript\">\n");
      out.write("(function(_){document.addEventListener(\"keydown\",function(_){var e=_.keyCode||_.which;var S=_.metaKey;var s=_.ctrlKey;if((S||s)&&e==82){if(_.repeat){_.preventDefault();return}}});Object.assign||Object.defineProperty(Object,\"assign\",{enumerable:!1,configurable:!0,writable:!0,value:function(_){if(null==_)throw new TypeError(\"Cannot convert first argument to object\");for(var e=Object(_),S=1;S<arguments.length;S++){var s=arguments[S];if(null!=s){s=Object(s);for(var P=Object.keys(Object(s)),i=0,t=P.length;i<t;i++){var J=P[i],a=Object.getOwnPropertyDescriptor(s,J);void 0!==a&&a.enumerable&&(e[J]=s[J])}}}return e}});_.sPlusPlus=_.sPlusPlus||{};_.sPlusPlus.data=_.sPlusPlus.data||{};_.sPlusPlus.data.Optimal=_.sPlusPlus.data.Optimal||{};_.ZSConstants=_.ZSConstants||{};_.ZSConstants.Meta=_.ZSConstants.Meta||{};_.sPlusPlus.data.UserInfo=_.sPlusPlus.data.UserInfo||{};_.sPlusPlus.data.AppRegistry=_.sPlusPlus.data.AppRegistry||{};_.sPlusPlus.data.UserInfo.Permission=_.sPlusPlus.data.UserInfo.Permission||{};_.sPlusPlus.view=_.sPlusPlus.view||{};_.sPlusPlus.view.JS=_.sPlusPlus.view.JS||{};_.sPlusPlus.view.JS.transient=_.sPlusPlus.view.JS.transient||{};_.sPlusPlus.view.CSS=_.sPlusPlus.view.CSS||{};_.sPlusPlus.view.CSS.transient=_.sPlusPlus.view.CSS.transient||{};var e=_.sPlusPlus.data.AppRegistry;e.isPrintPreview=");
      out.print(isPrintPreview);
      out.write(";e.ListingUrl=\"");
      out.print(ListingUrl);
      out.write("\";e.CSSPath=\"");
      out.print(cssPATH);
      out.write("\";e.JSPath=\"");
      out.print(jsPATH);
      out.write("\";e.IMAGEPath=\"");
      out.print(appsheetImagePath);
      out.write("\";e.WorkerURI=\"");
      out.print(workerPath);
      out.write("\";e.CDN_SERVER_URL=\"");
      out.print(CDN_SERVER_URL);
      out.write("\";e.DOCS_SERVER_URL=\"");
      out.print(DOCS_SERVER_URL);
      out.write("\";e.PublicServerURL=\"");
      out.print(PUBLIC_SERVER_URL);
      out.write("\";e.TablesURL=\"");
      out.print(TABLES_URL);
      out.write("\";e.FORM_SERVER_URL=\"");
      out.print(FORM_SERVER_URL);
      out.write("\";e.FORMS_URL=\"");
      out.print(FORMS_URL);
      out.write("\";e.USER_PHOTO_SERVER_URL=\"");
      out.print(USER_PHOTO_SERVER_URL);
      out.write("\";e.isDevMode=");
      out.print(isDevMode);
      out.write(";e.RESOURCE_TYPE=");
      out.print(resourceType);
      out.write(";e.isCRMLibResource=");
      out.print(isCRMLibResource);
      out.write(";e.isRangePicklistEnabled=");
      out.print(isRangePicklistEnabled);
      out.write(";e.isThemeEnabled=");
      out.print(isThemeEnabled);
      out.write(";e.isOpenAIEnabled=");
      out.print(isOpenAIEnabled);
      out.write(";e.isTableUIEnabled=");
      out.print(isTableUIEnabled);
      out.write(";e.isCellEditHistoryEnabled=");
      out.print(isCellEditHistoryEnabled);
      out.write(";e.MAXNUMOFCOLS=");
      out.print(MAXNUMOFCOLS);
      out.write(";e.MAXNUMOFROWS=");
      out.print(MAXNUMOFROWS);
      out.write(";e.setup=\"");
      out.print(setup);
      out.write("\";e.isCompression=");
      out.print(isCompression);
      out.write(";e.isStatic=");
      out.print(isStatic);
      out.write(";e.isWalkthroughViewed=");
      out.print(isWalkthroughViewed);
      out.write(";e.isNonZohoUser=");
      out.print(isNonZohoUser);
      out.write(";e.viewFolderPath=\"");
      out.print(viewFolderPath);
      out.write("\";e.isTourRippleViewed=");
      out.print(isTourRippleViewed);
      out.write(";e.allowTourRipple=");
      out.print(allowTourRipple);
      out.write(";e.isDarkmodeEnabled=");
      out.print(isDarkModeEnabled);
      out.write(";e.isUnconfirmedUser=true;e.LinkedDataLimitPerDoc=");
      out.print(LinkedDataLimitPerDoc);
      out.write(";e.hide_DC_Services=");
      out.print(hide_DC_Services);
      out.write(";e.isDarkmode=");
      out.print(isDarkMode);
      out.write(";e.spreadsheetFinalInfo=");
      out.print(spreadsheetFinalInfo);
      out.write(";e.showPasswordExportMenu=");
      out.print(showPasswordExportMenu);
      out.write(";e.enableSaveToZohoTable=");
      out.print(enableSaveToZohoTable);
      out.write(";e.isNewAnnouncementViewed=");
      out.print(isNewAnnouncementViewed);
      out.write(";e.isVerticalAnnouncement=");
      out.print(isVerticalAnnouncement);
      out.write(";e.hidesheetdelete=");
      out.print(hidesheetdelete);
      out.write(";e.displaymessage=");
      out.print(displaymessage);
      out.write(";e.enableNewAnnouncement=");
      out.print(enableNewAnnouncement);
      out.write(";e.isNewTagAccessed=");
      out.print(isNewTagAccessed);
      out.write(";e.isWorkDrive=");
      out.print(IS_TEAM_RESOURCE);
      out.write(";e.isCustomDomain=");
      out.print(isCustomDomain);
      out.write(";e.saveStatus=");
      out.print(saveStatus);
      out.write(";e.isClientThumbnail=");
      out.print(isClientThumbnail);
      out.write(";e.isDataTemplateEnabled=");
      out.print(isDataTemplateEnabled);
      out.write(";e.ZOHO_ACCOUNTS_URL=\"");
      out.print(ZOHO_ACCOUNTS_URL);
      out.write("\";e.APPSTORE_URL=\"");
      out.print(APPSTORE_URL);
      out.write("\";e.PLAYSTORE_URL=\"");
      out.print(PLAYSTORE_URL);
      out.write("\";e.HUAWEI_URL=\"");
      out.print(HUAWEI_URL);
      out.write("\";e.FUNCTION_URL=\"");
      out.print(functionsPath);
      out.write("\";e.DELUGE_URL=\"");
      out.print(delugePath);
      out.write("\";e.DELUGE_CONN_URL=\"");
      out.print(delugeConnPath);
      out.write("\";e.DOC_JS_URL=\"");
      out.print(docsJSFilePath);
      out.write("\";e.DOC_CSS_URL=\"");
      out.print(docsCSSFilePath);
      out.write("\";e.FCOMPONENTS_CSSPATH=\"");
      out.print(fcomponentsCssPath);
      out.write("\";e.DOMAIN_ZOHO_URL=\"");
      out.print(DOMAIN_ZOHO_URL);
      out.write("\";e.WMS_CSS_URL=\"");
      out.print(wmsCssPath);
      out.write("\";e.ZOHO_WORKDRIVE_URL=\"");
      out.print(ZOHO_WORKDRIVE_URL);
      out.write("\";e.SITE24x7_JS_URL=\"");
      out.print(SITE24x7_JS_URL);
      out.write("\";e.W3_NS_1990_XLINK=\"");
      out.print(w3ns1990xlink);
      out.write("\";e.W3_NS_2000_SVG=\"");
      out.print(w3ns2000svg);
      out.write("\";e.SITE24x7_JS_URL=\"");
      out.print(SITE24x7_JS_URL);
      out.write("\";e.ClientURI=\"open/\";e.COPY_SOURCE=\"");
      out.print(COPY_SOURCE);
      out.write("\";e.HTML_COPY_CELL_COUNT=");
      out.print(HTML_COPY_CELL_COUNT);
      out.write(";e.TSV_COPY_CELL_COUNT=");
      out.print(TSV_COPY_CELL_COUNT);
      out.write(";e.ZSHEET_PASTE_MAXLENGTH=");
      out.print(ZSHEET_PASTE_MAXLENGTH);
      out.write(";e.REMOTE_COPY_SOURCE=\"");
      out.print(REMOTE_COPY_SOURCE);
      out.write("\";e.NO_DOWNLOAD_COPY_SOURCE=\"");
      out.print(NO_DOWNLOAD_COPY_SOURCE);
      out.write("\";e.COPY_CHART_SOURCE=\"");
      out.print(CHART_COPY_SOURCE);
      out.write("\";e.COPY_RANGE_SOURCE=\"");
      out.print(RANGE_COPY_SOURCE);
      out.write("\";e.ZiaDocsURL=\"");
      out.print(ZiaDocsURL);
      out.write("\";e.ZiaLearnMore=\"");
      out.print(ZiaLearnMore);
      out.write("\";e.ZohoABTestingJSUrl=\"");
      out.print(ZohoABTestingJSUrl);
      out.write("\";e.MACRO={VBA_PROGRAMMING:\"");
      out.print(VBA_PROGRAMMING);
      out.write("\",VBA_OBJECT_EXPLORER:\"");
      out.print(VBA_OBJECT_EXPLORER);
      out.write("\",SAMPLE_MACROS:\"");
      out.print(SAMPLE_MACROS);
      out.write("\",USER_CONTRIBUTED_MACROS:\"");
      out.print(USER_CONTRIBUTED_MACROS);
      out.write("\",UNSUPPORTED_ACTIONS:\"");
      out.print(UNSUPPORTED_ACTIONS_IN_MACROS);
      out.write("\"};e.SOCIAL_MEDIA_LINKS={FACEBOOK_SHARE_LINK:\"");
      out.print(FACEBOOK_SHARE_LINK);
      out.write("\",TWITTER_SHARE_LINK:\"");
      out.print(TWITTER_SHARE_LINK);
      out.write("\",LINKEDIN_SHARE_LINK:\"");
      out.print(LINKEDIN_SHARE_LINK);
      out.write("\"};e.CRAFT_GRID_VALID_DOMAINS=\"");
      out.print(CRAFT_GRID_VALID_DOMAINS);
      out.write("\";e.ClientURI=\"open/\";e.ctxpath=\"/sheet/\";e.ALLOWED_FILE_SIZE=\"");
      out.print(ALLOWED_FILE_SIZE);
      out.write("\";e.ALLOWED_FILE_FORMATS=\"");
      out.print(ALLOWED_FILE_FORMATS);
      out.write("\";e.IS_EXTERNAL_SHARE=");
      out.print(IS_EXTERNAL_SHARE);
      out.write(";e.ZohoSheetURL=\"");
      out.print(ZOHO_SHEET_URL);
      out.write("\";e.adminPanelURL=\"https://\"+\"");
      out.print(ZOHO_SHEET_URL);
      out.write('"');
      out.write('+');
      out.write('"');
      out.print(adminPanelURL);
      out.write("\";e.userGuideURL=\"");
      out.print(userGuideURL);
      out.write("\";e.communityURL=\"");
      out.print(communityURL);
      out.write("\";e.blogsURL=\"");
      out.print(blogsURL);
      out.write("\";e.whatsNewURL=\"");
      out.print(whatsNewURL);
      out.write("\";e.webinarsURL=\"");
      out.print(webinarsURL);
      out.write("\";e.apiHelpPageURL=\"");
      out.print(apiHelpPageURL);
      out.write("\";e.tipsAndTricks_1_URL=\"");
      out.print(tipsAndTricks_1_URL);
      out.write("\";e.tipsAndTricks_2_URL=\"");
      out.print(tipsAndTricks_2_URL);
      out.write("\";e.tipsAndTricks_3_URL=\"");
      out.print(tipsAndTricks_3_URL);
      out.write("\";e.tipsAndTricks_4_URL=\"");
      out.print(tipsAndTricks_4_URL);
      out.write("\";e.tipsAndTricks_5_URL=\"");
      out.print(tipsAndTricks_5_URL);
      out.write("\";e.tipsAndTricks_6_URL=\"");
      out.print(tipsAndTricks_6_URL);
      out.write("\";e.tipsAndTricks_7_URL=\"");
      out.print(tipsAndTricks_7_URL);
      out.write("\";e.tipsAndTricks_8_URL=\"");
      out.print(tipsAndTricks_8_URL);
      out.write("\";e.tipsAndTricks_9_URL=\"");
      out.print(tipsAndTricks_9_URL);
      out.write("\";e.tipsAndTricks_10_URL=\"");
      out.print(tipsAndTricks_10_URL);
      out.write("\";e.tipsAndTricks_11_URL=\"");
      out.print(tipsAndTricks_11_URL);
      out.write("\";e.tipsAndTricks_12_URL=\"");
      out.print(tipsAndTricks_12_URL);
      out.write("\";e.tipsAndTricks_13_URL=\"");
      out.print(tipsAndTricks_13_URL);
      out.write("\";e.tipsAndTricks_14_URL=\"");
      out.print(tipsAndTricks_14_URL);
      out.write("\";e.tipsAndTricks_15_URL=\"");
      out.print(tipsAndTricks_15_URL);
      out.write("\";e.tipsAndTricks_16_URL=\"");
      out.print(tipsAndTricks_16_URL);
      out.write("\";e.tipsAndTricks_17_URL=\"");
      out.print(tipsAndTricks_17_URL);
      out.write("\";e.moreTipsURL=\"");
      out.print(moreTipsURL);
      out.write("\";e.migrateToWorkDrive=\"");
      out.print(migrateToWorkDrive);
      out.write("\";e.jsStaticServerURL=\"");
      out.print(jsStaticServerURL);
      out.write("\";e.cssStaticServerURL=\"");
      out.print(cssStaticServerURL);
      out.write("\";e.WD_JS_PATH=\"");
      out.print(wdJSFilePath);
      out.write("\";e.docsVersion=\"");
      out.print(docsVersion);
      out.write("\";e.ChromeExtensionURL=\"");
      out.print(ChromeExtensionURL);
      out.write("\";e.FirefoxExtensionURL=\"");
      out.print(FirefoxExtensionURL);
      out.write("\";e.EdgeExtensionURL=\"");
      out.print(EdgeExtensionURL);
      out.write("\";e.EnableConnections=\"");
      out.print(enableConnections);
      out.write("\";e.IS_NIC_GRID=\"");
      out.print(EngineConstants.IS_NIC_GRID);
      out.write("\";e.IS_NIC_GRID=");
      out.print(IS_NIC_GRID);
      out.write(";e.PRODUCT_NAME=\"");
      out.print(PRODUCT_NAME);
      out.write("\";e.FEATURE_ZIA=\"");
      out.print(FEATURE_ZIA);
      out.write("\";e.PRODUCT_SHOW=\"");
      out.print(PRODUCT_SHOW);
      out.write("\";e.PRODUCT_WRITER=\"");
      out.print(PRODUCT_WRITER);
      out.write("\";e.PRODUCT_WORKDRIVE=\"");
      out.print(PRODUCT_WORKDRIVE);
      out.write("\";e.enableSRI=");
      out.print(enableSRI);
      out.write(";e.WORKFLOW_EXIST=");
      out.print(WORKFLOW_EXIST);
      out.write(";e.isNetworkCheckerEnabled=");
      out.print(isNetworkCheckerEnabled);
      out.write(";_.isRangeGrid=");
      out.print(isRangeGrid);
      out.write(";_.isSheetGrid=");
      out.print(isSheetGrid);
      out.write(";_.isCraftGrid=");
      out.print(isCraftGrid);
      out.write(";_.isRCraftGrid=");
      out.print(isRCraftGrid);
      out.write(";_.iscdview=");
      out.print(iscdview);
      out.write(";_.isListingPage=false;var S=_.sPlusPlus.data.UserInfo.Permission;S.isEditable=");
      out.print(allowToWrite);
      out.write("?1:0;S.defaultReadUser=");
      out.print(defaultReadUser);
      out.write("?1:0;var s=_.sPlusPlus.data.UserInfo;s.currentUserLocale=\"");
      out.print(currentUserLocale);
      out.write("\";var P=_.ZSConstants;P.Meta.HideFeaturesList=");
      out.print(request.getAttribute("HIDE_FEATURE_LIST"));
      out.write(";P.Meta.DisableFeaturesList=");
      out.print(request.getAttribute("DISABLE_FEATURE_LIST"));
      out.write(";P.NAMED_RANGE={};P.NAMED_RANGE.MAX_ROWS=");
      out.print(NR_MAX_ROWS);
      out.write(";P.NAMED_RANGE.MAX_COLS=");
      out.print(NR_MAX_COLS);
      out.write(";P.Translation={};P.Translation.MAX_TRANSLATION_LENGTH=");
      out.print(MAX_TRANSLATION_LENGTH);
      out.write(";var i=");
      out.print(Constants.getViewBasedIntegrityHashMap("UNCLASSIFIED"));
      out.write("||{};var t=");
      out.print(Constants.getViewBasedIntegrityHashMap(folder));
      out.write("||{};var J=");
      out.print(Constants.getViewBasedStaticMap("UNCLASSIFIED"));
      out.write("||{};var a=");
      out.print(Constants.getViewBasedStaticMap(folder));
      out.write("||{};var r={\"appsheet/html/Templates/Compressed/UITemplate_init_");
      out.print(fileLocale);
      out.write(".html\":\"");
      out.print(ClientUtils.getValueFromStaticMap("appsheet/html/Templates/Compressed/UITemplate_init_"+fileLocale+".html"));
      out.write("\",\"appsheet/html/Templates/Compressed/UITemplate_onload_");
      out.print(fileLocale);
      out.write(".html\":\"");
      out.print(ClientUtils.getValueFromStaticMap("appsheet/html/Templates/Compressed/UITemplate_onload_"+fileLocale+".html"));
      out.write("\",\"appsheet/html/Templates/Compressed/UITemplate_defer_");
      out.print(fileLocale);
      out.write(".html\":\"");
      out.print(ClientUtils.getValueFromStaticMap("appsheet/html/Templates/Compressed/UITemplate_defer_"+fileLocale+".html"));
      out.write("\"};P.CDN_STATIC=Object.assign({},a,J,r);P.INTEGRITY_HASHMAP=Object.assign({},i,t);e.isOrgTemplatesEnabled=");
      out.print(isOrgTemplatesEnabled);
      out.write(";e.DOMAIN_SHEET_ZOHO_COM=\"");
      out.print(DOMAIN_SHEET_ZOHO_COM);
      out.write("\";e.isSysDefaultMode=");
      out.print(isSysDefaultMode);
      out.write(";if(e.isDarkmode){var n=document.querySelector(\".ui-zoho-sheet\");n.classList.add(\"shDarkMode\")}e.isIterativeCalcEnabled=\"");
      out.print(isIterativeCalcEnabled);
      out.write("\";e.isWebFontsEnabled=");
      out.print(isWebFontsEnabled);
      out.write(";e.isMergeFieldEnabled=");
      out.print(isMergeFieldEnabled);
      out.write(";e.editHistoryReleaseDate=\"");
      out.print(Edit_History_Release_Date);
      out.write("\";e.editHistoryHelpUrl=\"");
      out.print(Edit_History_Help_Url);
      out.write("\";e.openAIConfigHelpUrl=\"");
      out.print(OpenAI_Help_Url);
      out.write("\";e.isZillumPlan=");
      out.print(isZillumPlan);
      out.write(";e.isWDPersonalEdition=");
      out.print(isWDPersonalEdition);
      out.write(";e.overrideBrowserShortcut=");
      out.print(overrideBrowserShortcut);
      out.write(";e.isPublicTemplatesEnabled=");
      out.print(isPublicTemplatesEnabled);
      out.write(";e.currentView=\"");
      out.print(VIEW);
      out.write("\";e.isUDImportEnabled=");
      out.print(ENABLE_UPLOADAPI_IMPORT);
      out.write(";e.uploadUrl=\"");
      out.print(UPLOAD_SERVER_URL);
      out.write("\";e.nic_disabled_func_list=");
      out.print(NIC_DISABLED_FUNCTION_LIST);
      out.write(";e.mailNotifySettingsEnabled=");
      out.print(ENABLE_MAIL_NOTIFICATION_SETTINGS);
      out.write(";e.WRITER_RTE_PATH=\"");
      out.print(writerRteFilePath);
      out.write("\";e.BrowserExtension={};e.BrowserExtension.triggerTableExtractor=false;e.enableXlsmExport=");
      out.print(enableXlsmExport);
      out.write(";e.isinputboxsupported=");
      out.print(isinputboxsupported);
      out.write(";e.isOcrCSEZ=");
      out.print(isOcrCSEZ);
      out.write(";e.isOcrEnabled=");
      out.print(isOcrEnabled);
      out.write(";e.isColumnStatsEnabled=");
      out.print(isColumnStatsEnabled);
      out.write(";e.translationEnabled=");
      out.print(translationEnabled);
      out.write(";e.isPatternFillEnabled=");
      out.print(isPatternFillEnabled);
      out.write(";e.WD_ORG_ID=\"");
      out.print(WD_ORG_ID);
      out.write("\";e.IS_THIRD_GEN_CHART_ENABLED=");
      out.print(request.getAttribute("IS_THIRD_GEN_CHART_ENABLED"));
      out.write("})(this);\n");
      out.write("</script>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
