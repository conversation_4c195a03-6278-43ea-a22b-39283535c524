/*$Id$*/

/*{
	NAME					: "LYPasteWidgetLayer.js",

	DATE					: "???",

	ABOUT					: "???",

	PURPOSE					: "???",

	HOW_TO_USE					: "???",

	DEPENDENT					: [""],

	CONTEXT					: "MAIN",

	DEFER					: "NO",

	TYPE					: "Productivity",

	PLAYER					: "DOM",

	DATA_STRUCTURE				: "???",

	EXPOSE					: "???",

	REVIEWER					: "<EMAIL>"

}*/

"use strict";

(function(ctx) {
	
	var ZSClass   	= ctx.ZSClass;
	var sPP       	= ctx.sPlusPlus;
	var LY        	= ZSClass.Layer || (ZSClass.Layer = {});
	var Interface 	= LY.Interface;
	var Modules   	= LY.Modules;
	var Main      	= Modules.Main;
	var Focus     	= Modules.Focus;
	var Resize    	= Modules.Resize;
	var Move      	= Modules.Move;

	var PasteWidget 	= LY.PasteWidget || (LY.PasteWidget = {});
	var UILayer   	= ZSClass.UILayer;
	var Model     	= PasteWidget.Model;

	var popOptionvalue = ["source_format", "destination_format","SEPARATOR", "T2T", "CONVERT_TO_TABLE", "ALL", "VALUES", "FORMATS", "FORMULAS", "COMMENTS", "VALUES_AND_NUMBERFORMATS", "FORMULAS_AND_NUMBERFORMATS", "ALL_EXCEPT_COMMENTS", "ALL_EXCEPT_BORDERS", "VALIDATION", "FILL_DEFAULT", "FILL_COPY", "FILL_FORMATS", "FILL_EXCEPT_FORMATS"];
	var popOptionMsg = ["CopyPasteMatchWithSource", "CopyPasteMatchWithDestination","SEPARATOR","TexttoColumns.Title", "Table.Dialog.Create", "MenuItem.HomeTab.Paste.All", "MenuItem.HomeTab.Paste.Values", "MenuItem.HomeTab.Paste.Formats", "MenuItem.HomeTab.Paste.Formulas", "MenuItem.HomeTab.Paste.Notes", "MenuItem.HomeTab.Paste.ValuesAndNumberFormats", "MenuItem.HomeTab.Paste.FormulasAndNumberFormats", "MenuItem.HomeTab.Paste.AllExceptNotes", "MenuItem.HomeTab.Paste.AllExceptBorders", "MenuItem.HomeTab.Paste.Validation", "FillSeries.Default", "FillSeries.CopyCells", "FillSeries.FillFormats", "FillSeries.FillWithOutFormats"];
    	var FillSeriesTypeMappingWithOption = {"copy":"FILL_COPY","default":"FILL_DEFAULT"};

	var holder          = document.getElementById("popupmenu");
	var UIComponent     = ctx.UIUtil.Component;
	var FC              = ZSConstants.FeatureConstants;
	var ActionConstants = ctx.ZSConstants.ActionConstants;
	var Recast          = sPP.view.Toolbar.Feature[FC.RECAST];
	var TEXT_TO_COLUMN 	= sPP.view.Toolbar.Feature[FC.TEXT_TO_COLUMN];
	var jsMsg           = ctx.ZSConstants.I18N;
	var listHolder      = null;
	var preActionId     = -1;
    var Events = ctx.ZSEvents;
	var Topic = Events.topic;
	var Groups = Events.Groups;
	var Names = Events.Names;

	function PasteWidgetLayer(id, parent, Controller, router) {
		Main.Layer.call(this, id, parent, Controller, router);		
		this.addRecastButton();
		this.addRecastOption();
		this.Dom.addEventListener("mousedown", function(ev) {
			ev.stopPropagation();
			UIComponent.triggerEvent(ev, document);
		});
		UILayer.call(this, this.Dom, Model);
	}

	PasteWidgetLayer.prototype = {
		addRecastButton : function() {
			var widgetDiv = document.createElement('div');
			widgetDiv.className = "pasteWid";
			var widgetBtn = document.createElement('div')
			widgetBtn.className = "shIcon cpWidget";
			widgetDiv.setAttribute("data-menu-id", "pasteWidgetPopUp");
			widgetDiv.appendChild(widgetBtn);
			widgetDiv.addEventListener("mousedown", function(ev) {
				ev.stopPropagation();
				UIComponent.triggerEvent(ev, document);
			});
			this.pasteWid = widgetDiv;
			this.Dom.appendChild(widgetDiv);
		},
		createListHolder : function() {
			listHolder = document.createElement('ul');
			listHolder.setAttribute("data-ctype", "zmenu");
			listHolder.id = "pasteWidgetPopUp";
			listHolder.setAttribute("data-content-type", "icon-text"); //No I18N
			for (var i = 0; i < popOptionMsg.length; i++) {
				var menuItem = document.createElement('li');
					menuItem.id = "menuItem" + i;
				if( popOptionvalue[i] === "SEPARATOR" ){
                    menuItem.setAttribute("data-item-type", "separator");
				}else{
					menuItem.setAttribute("data-item-type", "checkbox");
					menuItem.setAttribute("data-value", popOptionvalue[i]);
					menuItem.innerText = jsMsg[popOptionMsg[i]];
				}
				listHolder.appendChild(menuItem);
			}
			holder.appendChild(listHolder);
			this.bindEvents();
		},
		addRecastOption : function() {
			!listHolder && this.createListHolder();
		},
		bindEvents : function() {
			UIComponent.bind(listHolder, "zmenuitemclick", function(ev, ui) {
				var list = document.getElementById(listHolder.id).getElementsByTagName("li");
				for (var i = 0; i < list.length; i++) {
					if (list[i].getAttribute("checked") === "checked") {
						UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#" + list[i].id], 'checked', false); //No I18N
					}
				}
				UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#" + ui.data.id], 'checked', true); //No I18N
				var gridID = sPP.view.getActiveGridID();
				var rID = sPP.view.getResourceID(gridID);
				var sheetList = sPP.view.getSelectedSheets(gridID);
				var version = sPP.view.getVersionID(gridID);
               
				if(ui.data.id === "menuItem3"){
					TEXT_TO_COLUMN = (TEXT_TO_COLUMN || sPP.view.Toolbar.Feature[FC.TEXT_TO_COLUMN]);
					var sheetID = sPP.view.getSheetID(gridID);
					var range_  = sPP.data.RecastInfo.aparams.rangeList[0][0];
					var pastedRange = new ZSClass.Range(range_.sr, range_.sc, range_.er, range_.ec);
					Topic.publish(Groups.Selector.BoundUpdate.Range, Names.Selection.BOUND_UPDATE.ADD,{"RID":rID, "SHEETID":sheetID, VERSION:version, "RANGE":pastedRange});   //No I18N
					// TEXT_TO_COLUMN.openDialog();
					ctx.JSLazyLoader.loadScript(ctx.JSLazyLoaderKeys.text_to_column, function() {
						TEXT_TO_COLUMN.openDialog();
					});
				}
				else if(ui.data.id === "menuItem4"){
					// Handle Convert To Table option
					var TABLE_FEATURE = sPP.view.Toolbar.Feature[FC.TABLE];
					if(TABLE_FEATURE) {
						var sheetID = sPP.view.getSheetID(gridID);
						var range_  = sPP.data.RecastInfo.aparams.rangeList[0][0];
						var pastedRange = new ZSClass.Range(range_.sr, range_.sc, range_.er, range_.ec);
						Topic.publish(Groups.Selector.BoundUpdate.Range, Names.Selection.BOUND_UPDATE.ADD,{"RID":rID, "SHEETID":sheetID, VERSION:version, "RANGE":pastedRange});   //No I18N
						TABLE_FEATURE.openDialog();
					}
				}
				else
				{
					Recast = (Recast || sPP.view.Toolbar.Feature[FC.RECAST]);
					Recast.recastAction(rID, sheetList, version, ev, ui);
				}



			})
			
			listHolder.addEventListener("mousedown", function(ev) {
				ev.stopPropagation();
			});

			UIComponent.bind(listHolder, "zmenubeforeshow", function(ev, ui) {
				var list = document.getElementById(listHolder.id).getElementsByTagName("li");
				if (preActionId !== sPP.data.RecastInfo.actionId) {
					preActionId = sPP.data.RecastInfo.actionId;
					for (var i = 0; i < list.length; i++) {
						if (list[i].getAttribute("checked") === "checked") {
							UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#" + list[i].id], 'checked', false); //No I18N
						}
					}
					switch (sPP.data.RecastInfo.action) {
						case ActionConstants.SYSTEMCLIP_PASTE:
							UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem0"], 'checked', true); //No I18N
							break;
						case ActionConstants.SERVERCLIP_PASTE_RANGE:
						case ActionConstants.COPY_PASTE:
							UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem"+popOptionvalue.indexOf(sPP.data.RecastInfo.popUpOptionType)], 'checked', true); //No I18N
							break;
						case ActionConstants.FILLSERIES:
							UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem"+popOptionvalue.indexOf(FillSeriesTypeMappingWithOption[sPP.data.RecastInfo.popUpOptionType])], 'checked', true); //No I18N
							break;
					}
				}
				for (var i = 0; i < list.length; i++) {
					list[i].style.display = "none";
				}
				switch (sPP.data.RecastInfo.action) {
					case ActionConstants.SYSTEMCLIP_PASTE:
						for (var i = 0; i < 2; i++) {
							list[i].style.display = "";
						}
						if(sPP.data.RecastInfo.isT2COption){
							list[2].style.display = "";
							list[3].style.display = "";
						}
						// Show Convert To Table option for system clipboard paste
						if(sPlusPlus.data.AppRegistry && sPlusPlus.data.AppRegistry.isTableUIEnabled) {
							list[4].style.display = "";
						}
						break;
					case ActionConstants.SERVERCLIP_PASTE_RANGE:
					case ActionConstants.COPY_PASTE:
						// Show Convert To Table option for copy-paste operations
						if(sPlusPlus.data.AppRegistry && sPlusPlus.data.AppRegistry.isTableUIEnabled) {
							list[4].style.display = "";
						}
						for (var i = 5; i < 15; i++) {
							list[i].style.display = "";
						}
						break;
					case ActionConstants.FILLSERIES:
						// Do NOT show Convert To Table option for fill series operations
						for (var i = 15; i < list.length; i++) {
							list[i].style.display = "";
						}
						break;
				}
			});
		},
		setDirection : function(isLeftDirection, isTopDirection, sr, sc, visibleEndRow, visibleEndCol) {
			var gridID = sPP.view.getActiveGridID();
			var gridComponent = sPP.view.getGridComponent(gridID);
			var interacter = gridComponent.interacter;
			var zoom = gridComponent.getZoom();
			if(isLeftDirection) {
				 var sLeft = (interacter.getColX(visibleEndCol) - interacter.getColX(sc+1))*zoom;
				 var zleft = sLeft < 0 ? 0 : sLeft;
				 this.pasteWid.style.left = zleft + "px" ;
			} else {
				this.pasteWid.style.left = "100%" ;
			}

			if(isTopDirection) {
				var sTop = (interacter.getRowY(visibleEndRow) - interacter.getRowY(sr+1))*zoom;
				var ztop = sTop < 0 ? 0 : sTop;  
				 this.pasteWid.style.top = ztop + "px" ;
			} else {
				this.pasteWid.style.top = "100%" ;
			}
		}
	};

	var Interfaces        = [Main.Layer, Focus.Layer];
	PasteWidgetLayer.prototype = Interface.implement(PasteWidgetLayer, Interfaces);

	PasteWidget.Layer = PasteWidgetLayer;
})(this);
