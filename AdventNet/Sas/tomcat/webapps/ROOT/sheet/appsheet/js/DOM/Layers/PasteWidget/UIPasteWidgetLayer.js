/*$Id$*/

/*{
	NAME					: "UIPasteWidgetLayer.js",

	DATE					: "???",

	ABOUT					: "???",

	PURPOSE					: "???",

	HOW_TO_USE				: "???",

	DEPENDENT				: [""],

	CONTEXT					: "MAIN",

	DEFER					: "NO",

	TYPE					: "Productivity",

	PLAYER					: "DOM",

	DATA_STRUCTURE				: "???",

	EXPOSE					: "???",

	REVIEWER				: "tiku"

}*/
"use strict";
(function(ctx) {

	if(ctx.isCanvasGrid){
		return;
	}
	var ZSClass = ctx.ZSClass;
	var Layer = ZSClass.UILayer;
	var holder = document.getElementById("popupmenu");
	var UIComponent = ctx.UIUtil.Component;
	var FC = ZSConstants.FeatureConstants;
	var ActionConstants = ctx.ZSConstants.ActionConstants;
	var sPP = ctx.sPlusPlus;
	var Recast = sPP.view.Toolbar.Feature[FC.RECAST];
	var jsMsg = ctx.ZSConstants.I18N;
	var listHolder = null;
	var preActionId = -1;
	var C 			    = ctx.ZSConstants.FeatureConstants
    var TEXT_TO_COLUMN 	= sPP.view.Toolbar.Feature[C.TEXT_TO_COLUMN];
	var popOptionvalue = ["source_format", "destination_format","SEPARATOR","T2T", "CONVERT_TO_TABLE", "ALL", "VALUES", "FORMATS", "FORMULAS", "COMMENTS", "VALUES_AND_NUMBERFORMATS", "FORMULAS_AND_NUMBERFORMATS", "ALL_EXCEPT_COMMENTS", "ALL_EXCEPT_BORDERS", "VALIDATION", "FILL_DEFAULT", "FILL_COPY", "FILL_FORMATS", "FILL_EXCEPT_FORMATS"];
	var popOptionMsg = ["CopyPasteMatchWithSource", "CopyPasteMatchWithDestination","SEPARATOR","TexttoColumns.Title", "Table.Dialog.Create", "MenuItem.HomeTab.Paste.All", "MenuItem.HomeTab.Paste.Values", "MenuItem.HomeTab.Paste.Formats", "MenuItem.HomeTab.Paste.Formulas", "MenuItem.HomeTab.Paste.Notes", "MenuItem.HomeTab.Paste.ValuesAndNumberFormats", "MenuItem.HomeTab.Paste.FormulasAndNumberFormats", "MenuItem.HomeTab.Paste.AllExceptNotes", "MenuItem.HomeTab.Paste.AllExceptBorders", "MenuItem.HomeTab.Paste.Validation", "FillSeries.Default", "FillSeries.CopyCells", "FillSeries.FillFormats", "FillSeries.FillWithOutFormats"];
    var FillSeriesTypeMappingWithOption = {"copy":"FILL_COPY","default":"FILL_DEFAULT"};
    var menuInfoObject = {"SYSTEMCLIP_PASTE":{'s':0,'e':4},"SERVERCLIP_PASTE_RANGE" :{'s':4,'e':14},"FILLSERIES":{'s':15,'e':18}};
    var Events = ctx.ZSEvents;
	var Topic = Events.topic;
	var Groups = Events.Groups;
	var Names = Events.Names;
	
	function UIPasteWidgetLayer(parent, properties) {
		Layer.call(this, parent, properties);
		this.addRecastButton();
		this.addRecastOption();
		this.Dom.addEventListener("mousedown", function(ev) {
			ev.stopPropagation();
			UIComponent.triggerEvent(ev, document);
		});
	}

	UIPasteWidgetLayer.prototype = new Layer();
    
    UIPasteWidgetLayer.prototype.setDirection = function(left, top) {
		if(left){
			this.Dom.classList.add('pasteLeft');
		}else {
			this.Dom.classList.remove('pasteLeft');
		}

		if(top){
			this.Dom.classList.add('pasteTop');
		}else {
			this.Dom.classList.remove('pasteTop');
		}
	}; 
	UIPasteWidgetLayer.prototype.addRecastButton = function() {
		var widgetDiv = document.createElement('div');
		widgetDiv.className = "pasteWid";
		var widgetBtn = document.createElement('div')
		widgetBtn.className = "shIcon cpWidget";
		widgetBtn.setAttribute("data-menu-id", "pasteWidgetPopUp");
		widgetDiv.appendChild(widgetBtn);
		widgetBtn.addEventListener("mousedown", function(ev) {
			ev.stopPropagation();
			UIComponent.triggerEvent(ev, document);
		});
		this.Dom.appendChild(widgetDiv);
	};

	UIPasteWidgetLayer.prototype.createListHolder = function(){
		if(holder){
			listHolder = document.createElement('ul');
			listHolder.setAttribute("data-ctype", "zmenu");
			listHolder.id = "pasteWidgetPopUp";
			listHolder.setAttribute("data-content-type", "icon-text"); //No I18N
			for (var i = 0; i < popOptionMsg.length; i++) {
				var menuItem = document.createElement('li');
				menuItem.id = "menuItem" + i;
				if( popOptionvalue[i] === "SEPARATOR" ){
                    menuItem.setAttribute("data-item-type", "separator");
				}else{
					menuItem.setAttribute("data-item-type", "checkbox");
					menuItem.setAttribute("data-value", popOptionvalue[i]);
					menuItem.innerText = jsMsg[popOptionMsg[i]];
			    }
				listHolder.appendChild(menuItem);
			}
			holder.appendChild(listHolder);
			bindEvents.call(this);
		}
	}

	//<li mtype="separator" class="zmenu__item" role="menuitem" data-item-type="undefined" data-label="" aria-hidden="false" aria-disabled="false"><span class="zmenu__text"></span></li>




	UIPasteWidgetLayer.prototype.addRecastOption = function() {
		!listHolder && this.createListHolder();		
		
	}

	function bindEvents(){
		UIComponent.bind(listHolder, "zmenuitemclick", function(ev, ui) {
			var list = document.getElementById(listHolder.id).getElementsByTagName("li");
			for (var i = 0; i < list.length; i++) {
				if (list[i].getAttribute("checked") === "checked" && "menuItem3" !== ui.data.id) {
					UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#" + list[i].id], 'checked', false); //No I18N
				}
			}
			if("menuItem3" === ui.data.id){
				UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem3"], 'checked', false); //No I18N
			}
            if("menuItem3" !== ui.data.id){
			  UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#" + ui.data.id], 'checked', true); //No I18N
	     	}
			var gridID = sPP.view.getActiveGridID();
			var rID = sPP.view.getResourceID(gridID);
			var sheetList = sPP.view.getSelectedSheets(gridID);
			var version = sPP.view.getVersionID(gridID);
			if(ui.data.id === "menuItem3"){
				var sheetID = sPP.view.getSheetID(gridID);
				var range_  = sPP.data.RecastInfo.aparams.rangeList[0][0];
				var pastedRange = new ZSClass.Range(range_.sr, range_.sc, range_.er, range_.ec);
				Topic.publish(Groups.Selector.BoundUpdate.Range, Names.Selection.BOUND_UPDATE.ADD,{"RID":rID, "SHEETID":sheetID, VERSION:version, "RANGE":pastedRange});   //No I18N
				// TEXT_TO_COLUMN.openDialog();
				ctx.JSLazyLoader.loadScript(ctx.JSLazyLoaderKeys.text_to_column, function() {
					TEXT_TO_COLUMN.openDialog();
				});
			}
			else if(ui.data.id === "menuItem4"){
				// Handle Convert To Table option
				var TABLE_FEATURE = sPP.view.Toolbar.Feature[C.TABLE];
				if(TABLE_FEATURE) {
					var sheetID = sPP.view.getSheetID(gridID);
					var range_  = sPP.data.RecastInfo.aparams.rangeList[0][0];
					var pastedRange = new ZSClass.Range(range_.sr, range_.sc, range_.er, range_.ec);
					Topic.publish(Groups.Selector.BoundUpdate.Range, Names.Selection.BOUND_UPDATE.ADD,{"RID":rID, "SHEETID":sheetID, VERSION:version, "RANGE":pastedRange});   //No I18N
					TABLE_FEATURE.openDialog();
				}
			}
			else
			{
				Recast.recastAction(rID, sheetList, version, ev, ui);
			}
		})
		
		listHolder.addEventListener("mousedown", function(ev) {

			ev.stopPropagation();
		});

		UIComponent.bind(listHolder, "zmenubeforeshow", function(ev, ui) {
			var list = document.getElementById(listHolder.id).getElementsByTagName("li");
			if (preActionId !== sPP.data.RecastInfo.actionId) {
				preActionId = sPP.data.RecastInfo.actionId;
				for (var i = 0; i < list.length; i++) {
					if (list[i].getAttribute("checked") === "checked") {
						UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#" + list[i].id], 'checked', false); //No I18N
					}
				}
				switch (sPP.data.RecastInfo.action) {
					case ActionConstants.SYSTEMCLIP_PASTE:
						UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem0"], 'checked', true); //No I18N
						break;
					case ActionConstants.SERVERCLIP_PASTE_RANGE:
					case ActionConstants.COPY_PASTE:
						UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem"+popOptionvalue.indexOf(sPP.data.RecastInfo.popUpOptionType)], 'checked', true); //No I18N
						break;
					case ActionConstants.FILLSERIES:
						UIComponent.zmenu("#" + listHolder.id, "setMenuItemsAttribute", ["#menuItem"+popOptionvalue.indexOf(FillSeriesTypeMappingWithOption[sPP.data.RecastInfo.popUpOptionType])], 'checked', true); //No I18N
						break;
				}
			}
			for (var i = 0; i < list.length; i++) {
				list[i].style.display = "none";
			}
			var sIndex = -1;
			var eIndex = -1;
			var hideMenuItemIndex = [];
			// Getting the start and end index of the menu to display and also collectecting the index of menu which need to be hide
			switch (sPP.data.RecastInfo.action) {
				case ActionConstants.SYSTEMCLIP_PASTE:
				    sIndex = menuInfoObject.SYSTEMCLIP_PASTE.s;
				    eIndex =  menuInfoObject.SYSTEMCLIP_PASTE.e;
				    if(sPP.data.RecastInfo.isT2COption && !sPP.data.RecastInfo.isRecastActionOption){
				    	hideMenuItemIndex.push(0);
				    	hideMenuItemIndex.push(1);
				    	hideMenuItemIndex.push(2);
				    }
				    if(!sPP.data.RecastInfo.isT2COption){
				    	hideMenuItemIndex.push(2);
				    	hideMenuItemIndex.push(3);
				    }
				    // Hide Convert To Table option if table UI is not enabled
				    if(!(sPlusPlus.data.AppRegistry && sPlusPlus.data.AppRegistry.isTableUIEnabled)) {
				    	hideMenuItemIndex.push(4);
				    }
					break;
				case ActionConstants.SERVERCLIP_PASTE_RANGE:
				case ActionConstants.COPY_PASTE:
				    sIndex =  menuInfoObject.SERVERCLIP_PASTE_RANGE.s;
				    eIndex =  menuInfoObject.SERVERCLIP_PASTE_RANGE.e;
				    // Hide Convert To Table option if table UI is not enabled
				    if(!(sPlusPlus.data.AppRegistry && sPlusPlus.data.AppRegistry.isTableUIEnabled)) {
				    	hideMenuItemIndex.push(4);
				    }
					break;
				case ActionConstants.FILLSERIES:
				    sIndex =  menuInfoObject.FILLSERIES.s;
				    eIndex =  menuInfoObject.FILLSERIES.e;
				    // Always hide Convert To Table option for fill series operations
				    hideMenuItemIndex.push(4);
					break;
			}
			// Do enable menu option[s]
			for (var i = sIndex; i <= eIndex; i++) {
					list[i].style.display = "";
			}
			// Do disable the  menu item[s]
			for (var i = 0; i < hideMenuItemIndex.length; i++) {
					list[hideMenuItemIndex[i]].style.display = "none";
			}
		});
	}
	ZSClass.UIPasteWidgetLayer = UIPasteWidgetLayer;

})(this);
