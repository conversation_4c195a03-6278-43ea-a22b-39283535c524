<%@page import="com.adventnet.iam.xss.IAMEncoder"%><%@page import="com.zoho.sheet.util.ClientUtils"%><%@page import="com.adventnet.iam.security.SecurityRequestWrapper"%><%String appSheetJSPath = request.getParameter("jsPATH");String folder = request.getParameter("folder");boolean isCompressed = Boolean.getBoolean("use.compression");if(isCompressed){ String filePath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_JS, "charts_old_defer_files.min.js", folder);String integrityKeyPath = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_JS, "charts_old_defer_files.min.js", folder, false);%><script  type="text/javascript" nonce = "<%=SecurityRequestWrapper.getInstance(request).getCSPNonce()%>">(function(ctx) {ctx.sPlusPlus.view.JS.transient.charts_old_defer_filesJS = ['<%=IAMEncoder.encodeJavaScript(filePath)%>'];})(this);</script><%} else { %><script type="text/javascript" nonce = "<%=SecurityRequestWrapper.getInstance(request).getCSPNonce()%>">(function(ctx) {ctx.sPlusPlus.view.JS.transient.charts_old_defer_filesJS = ["<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Productivity/State/Sheet/StateChart.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Listeners/ChartUIListeners.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>DOM/Toolbar/MenuItems/Chart/UIChartContextMenuOld.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>ContextMenu/Controller/UIChartContextMenuOld.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartAction.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/SidePanelManager.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartColorManager.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartPublishManager.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartActionUtility.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/PublishDialogManager.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartClip.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartDesignClip.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartClipManager.js","<%=IAMEncoder.encodeJavaScript(appSheetJSPath)%>Chart/ChartDataEncoder.js",];})(this);</script><%}%>