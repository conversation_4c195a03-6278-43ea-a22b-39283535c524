{"workbook": {"sub_list": ["List all workbooks", "List all templates", "List all versions", "Create workbook", "Create workbook from template", "Upload workbook", "Download workbook", "Insert images", "Copy workbook", "Share workbook", "Create version", "Revert version", "Trash workbook", "Restore workbook", "Delete workbook", "Publish", "Remove publish", "Lock", "Unlock"], "List all workbooks": {"description": "This API can be used to list all the workbooks (owned/shared) of the user. The parameter 'start_index' and 'count' can be used to get a specific set of workbooks. By default, the API returns a maximum of 1000 workbooks when count parameter is not used.", "is_docs_api": true, "url": "workbooks", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.list", "sample_data": "workbook.list"}, {"param_name": "start_index", "param_desc": "Optional Parameter. This parameter can be used to get a few resources if there are too many.", "sample_data": 1}, {"param_name": "count", "param_desc": "Optional Parameter. It denotes the number of resources in response.", "sample_data": 2}, {"param_name": "sort_option", "param_desc": "Optional Parameter. Supported options are 'recently_opened', 'recently_modified', 'recently_created', 'ascending', and 'descending'. Default option is 'recently_created'.", "sample_data": "recently_modified"}], "response": {"workbooks": [{"created_time": "Friday, 6 April, 2018 4:14:10 PM", "last_modified_time": "Friday, 6 April, 2018 4:14:10 PM", "workbook_name": "Test workbook", "workbook_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeeee", "resource_id": "aaaaabbbbbccccdddddeeeee", "created_by": "jack"}, {"created_time": "Friday, 6 April, 2018 4:13:36 PM", "last_modified_time": "Friday, 6 April, 2018 4:13:36 PM", "workbook_name": "Test shared workbook", "workbook_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddfffff", "resource_id": "aaaaabbbbbccccdddddfffff", "created_by": "john"}], "method": "workbook.list", "resource_start_index": 345, "resource_end_index": 346, "resource_count": 390, "status": "success"}}, "List all templates": {"description": "This API can be used to list all the templates of the user.", "is_docs_api": true, "url": "templates", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "template.list", "sample_data": "template.list"}], "response": {"templates": [{"created_time": "Friday, 6 April, 2018 4:14:10 PM", "last_modified_time": "Friday, 6 April, 2018 4:14:10 PM", "template_name": "Test workbook", "template_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeeee", "resource_id": "aaaaabbbbbccccdddddeeeee", "created_by": "jack"}, {"created_time": "Friday, 6 April, 2018 4:13:36 PM", "last_modified_time": "Friday, 6 April, 2018 4:13:36 PM", "template_name": "Test shared workbook", "template_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddfffff", "resource_id": "aaaaabbbbbccccdddddfffff", "created_by": "jack"}], "method": "template.list", "status": "success"}}, "List all versions": {"description": "This API can be used to list all the versions of a workbook.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.version.list", "sample_data": "workbook.version.list"}], "response": {"versions": [{"created_time": "Friday, 6 April, 2018 4:13:36 PM", "version_number": "1.0", "version_description": "v1", "created_by": "jack"}, {"created_time": "Friday, 6 April, 2018 4:14:10 PM", "version_number": "13.0", "version_description": "v2", "created_by": "jack"}], "method": "workbook.version.list", "status": "success"}}, "Create workbook": {"description": "This API can be used to add a new workbook. The name of the workbook file must be provided by the user.", "is_docs_api": true, "url": "create", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.create", "sample_data": "workbook.create"}, {"param_name": "workbook_name", "param_desc": "Name of the new workbook", "sample_data": "Sheet1"}, {"param_name": "parent_id", "param_desc": "Optional parameter. The unique ID of the destination folder. By default My Folder of Zoho Workdrive is the destination folder.", "sample_data": "aaaaacccccfffffggggghh"}], "response": {"workbook_name": "Sheet1", "resource_id": "aaaaabbbbbccccdddddeeee", "workbook_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeee", "worksheet_name": "Sheet1", "worksheet_id": "0#", "method": "workbook.create", "status": "success"}}, "Create workbook from template": {"description": "This API can be used to create a new workbook from a template.", "is_docs_api": true, "url": "createfromtemplate", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.createfromtemplate", "sample_data": "workbook.createfromtemplate"}, {"param_name": "resource_id", "param_desc": "The resource id of the template", "sample_data": "f234ri8i3fbnndfcvnsjhef"}, {"param_name": "workbook_name", "param_desc": "Optional parameter. Name of the new workbook. By default it will be same as name of the template.", "sample_data": "EmployeeDetails"}, {"param_name": "parent_id", "param_desc": "Optional parameter. The unique ID of the destination folder. By default My Folder of Zoho Workdrive is the destination folder.", "sample_data": "aaaaacccccfffffggggghh"}], "response": {"workbook_name": "Employee Details", "resource_id": "aaaaabbbbbccccdddddeeee", "workbook_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeee", "method": "workbook.createfromtemplate", "status": "success"}}, "Upload workbook": {"description": "This API can be used to upload a workbook file. The following file formats can be used for import:<br/>.xls, .xlsm, .csv, .tsv, .sxc, .ods.<br/>The maximum file size that can be imported is 10 MB.", "is_docs_api": true, "url": "upload", "multipart": true, "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.upload", "sample_data": "workbook.upload"}, {"param_name": "file", "param_desc": "Local file that needs to be uploaded. The parameter type is file.", "sample_data": ""}, {"param_name": "file_url", "param_desc": "If the user wants to upload file from a url this parameter can be used instead of file parameter.", "sample_data": "https://example.com"}, {"param_name": "workbook_name", "param_desc": "Name of the new workbook", "sample_data": "Sheet1"}, {"param_name": "delimiter", "param_desc": "Optional parameter and it is used only for CSV file type.", "sample_data": ""}, {"param_name": "date_format", "param_desc": "Optional parameter and it is used only for CSV file type.", "sample_data": ""}, {"param_name": "import_option", "param_desc": "Optional parameter. By default it creates new workbook. Possible import options are import_as_new_workbook, insert_as_worksheets", "sample_data": "import_as_new_workbook"}, {"param_name": "resource_id", "param_desc": "The parameter is equired when import_option is insert_as_worksheets.", "sample_data": "aaaaabbbbccccdddd"}], "response": {"workbook_name": "Sheet1", "resource_id": "aaaaabbbbbccccdddddeeee", "workbook_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeee", "method": "workbook.upload", "status": "success"}}, "Download workbook": {"description": "This API can be used to download an existing workbook. The file can be downloaded in the following formats:<br/>xlsx, csv, tsv, ods and pdf", "is_docs_api": true, "url": "download/<resource_id>", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "workbook.download", "sample_data": "workbook.download"}, {"param_name": "format", "param_desc": "Supported formats are xlsx, csv, tsv, ods and pdf", "sample_data": "pdf"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet when a particular sheet needs to be downloaded. This is an optional parameter and it can be used only with CSV, TSV and PDF file. ", "sample_data": "sheet1"}, {"param_name": "range", "param_desc": "A particular range can also be downloaded in CSV, TSV and PDF file format. Example : A1:E8", "sample_data": "A1:E8"}, {"param_name": "page_settings", "param_type": "json_object", "param_desc": "This is an optional parameter and can be used only with PDF file. JSONObject that contains the page settings/options. Possible options are \n <br>1.print_type = \"SHEET\" | \"WORKBOOK\" | \"RANGE\" <br/>2.orientation = 0 | 1, where 0 is Portrait and 1 is Landscape<br/>3.scale = 0 to 3 or value in %, where 0 is Default, 1 is Fit to width, 2 is Fit to height, 3 is Fit to Page<br/>4.header_content = Array of values. Eg.[\"Left Content\", \"Center Content\", \"Right Content\"]<br/>5.footer_content = Array of values. Eg.[\"Left Content\", \"Center Content\", \"Right Content\"]<br/>6.page_order = \"overDown\" | \"downOver\"<br/>7.align_vcenter, align_hcenter, add_gridlines, add_chart, add_image, add_button are all boolean values<br/>8.page_width, page_height, margin_left, margin_right, margin_top, margin_bottom are all numerical values.<br/>Eg:{\"print_type\": \"SHEET\",\"margin_left\": 0.5,\"margin_right\": 0.5,\"page_order\": \"overDown\",\"add_chart\": true}", "sample_data": {"print_type": "SHEET", "margin_left": 0.5, "margin_right": 0.5, "page_order": "overDown", "add_chart": true}}, {"param_name": "password", "param_desc": "Optional parameter. This parameter can be used to protect XLSX and PDF files with a password and is ignored for other formats.", "sample_data": "Abc123"}], "response": "DOWNLOADED FILE"}, "Insert images": {"description": "This API can be used to insert images into a workbook.<br/><br/>The parameter image_json can have the follwing keys.<p>row: Index position of the row where the image needs to be inserted. <p> column: Index position of the column.<p>worksheet_name and worksheet_id: Either worksheet_name or worksheet_id is required. <p>image_in_cell: Optional boolean parameter. The default value is true. Set it as false if you want to insert it over cell. <p>image_fit_option: Optional Parameter. The default value is fit. This option can be used only for cell images. Possible values are fit, stretch, or cover.<p>image_id: Optional parameter. Required when you try to insert an image which is already in the library.<p>image_file_name: Optional parameter. Required when you try to upload an image. The image file should be added with imagefiles parameter. Either image_file_name or image_id is required for processing.<p>add_to_library: Optional boolean parameter. Default value is false. Set it as true if you want the images to be added to the library. You will receive the image_id in response if you add it to library and next time you can use the image_id to insert the image. You can add an image to library if you want to insert same image in multiple documents or in multiple places of the same document.", "is_docs_api": true, "url": "insertimages", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "multipart": true, "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "workbook.images.insert", "sample_data": "workbook.images.insert"}, {"param_name": "resource_id", "param_desc": "The resource id of the workbook where the images needs to be inserted", "sample_data": "aaaaabbbbbccccdddddeeee"}, {"param_name": "image_json", "param_desc": "JSON Array example : [{\"row\":\"2\",\"column\":8,\"worksheet_name\":\"Sheet1\",\"insert_in_cell\":true, \"image_fit_option\":\"stretch\", \"image_id\":\"*****\"},{\"row\":1,\"column\":8,\"image_file_name\":\"flower.png\",\"worksheet_name\":\"Sheet1\",\n\"insert_in_cell\":true,\"image_fit_option\":\"fit\"}].", "param_type": "json_array", "sample_data": [{"row": 3, "column": 4, "worksheet_name": "Sheet1", "image_id": "65446766", "insert_in_cell": true, "add_to_library": true}]}, {"param_name": "imagefiles", "param_desc": "Local image files that needs to be uploaded. The parameter type is file.", "sample_data": ""}], "response": {"images": [{"column": 4, "row": 3, "image_file_name": "flower.png", "image_id": "65446766", "worksheet_name": "Sheet1"}], "method": "workbook.images.insert", "status": "success"}}, "Copy workbook": {"description": "This API can be used to make a new copy of an existing workbook.", "is_docs_api": true, "url": "copy", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.copy", "sample_data": "workbook.copy"}, {"param_name": "resource_id", "param_desc": "The resource id of the workbook that needs to be copied", "sample_data": "aaaaabbbbbccccdddddfffff"}, {"param_name": "workbook_name", "param_desc": "Optional parameter. Name of the copied workbook", "sample_data": "Copy of Test"}, {"param_name": "parent_id", "param_desc": "Optional parameter. The unique ID of the destination folder. By default My Folder of Zoho Workdrive is the destination folder.", "sample_data": "aaaaacccccfffffggggghh"}, {"param_name": "copy_lock_settings", "param_desc": "Optional parameter. Default value is true. If set to false the range/worksheet lock settings from the parent document will not be copied.", "sample_data": "true"}], "response": {"workbook_name": "Copy of Test", "resource_id": "aaaaabbbbbccccdddddeeee", "workbook_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeee", "method": "workbook.copy", "status": "success"}}, "Share workbook": {"description": "This API can be used to share a workbook with a user.<br/><br/> The access levels available for sharing are : <p/> 1 - share  (User has the permission to view, comment, edit, and share the workbook)<p/> 2 - edit (User has the permission to view, comment, and edit the workbook)<p/> 3 - view_and_comment  (User has the permission to view and make comment in the workbook)<p/> 4 - view (User has the permission to only view the workbook)<p/> 5 - remove_share (User has no permission)", "is_docs_api": true, "url": "share", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "workbook.share", "sample_data": "workbook.share"}, {"param_name": "resource_id", "param_desc": "The resource id of the workbook that needs to be shared.", "sample_data": "aaaaabbbbbccccdddddeeee"}, {"param_name": "share_json", "param_type": "json_array", "param_desc": "JSON Array consisting of the user's email id and the access level with which the user will be able to view the shared workbook. Example : [{\"user_email\":\"<EMAIL>\", \"access_level\":\"share\"}, {\"user_email\":\"<EMAIL>\", \"access_level\":\"view\"}]", "sample_data": [{"user_email": "<EMAIL>", "access_level": "share"}, {"user_email": "<EMAIL>", "access_level": "view"}]}], "response": {"method": "workbook.share", "status": "success"}}, "Create External share link": {"description": "This API can be used to create a link that can be shared to external users to help them view and edit in the workbook.", "is_docs_api": true, "url": "externalsharelink", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "8", "parameters": [{"param_name": "method", "param_desc": "workbook.externalsharelink"}, {"param_name": "resource_id", "param_desc": "The resource id of the workbook that needs to be shared."}, {"param_name": "link_name", "param_desc": "The name of the link for easy reference. Eg:Event Leads."}, {"param_name": "allow_download", "param_desc": "Optional parameter. If set to true, user with the share link will be able to download the workbook. It is set true by default."}, {"param_name": "password", "param_desc": "Optional parameter. If set, share link can only be accessed with the password."}, {"param_name": "expiration_date", "param_desc": "Optional parameter. If set, share link access will be deactivated after the expiry date. The format is YYYY-MM-DD(2020-09-26)."}, {"param_name": "access_level", "param_desc": "The access level with which the shared workbook will be available to the user. Access levels available are 1.edit 2.view"}, {"param_name": "request_user_data", "param_desc": "Optional parameter. If set, share link can only be accessed after providing the required details. The data that can be requested are 1.NAME 2.PHONE 3.EMAIL. Eg: NAME,EMAIL"}], "response": {"method": "workbook.externalsharelink", "link": "https://workdrive.zohoexternal.com/external/1Oxchacw73yd-J8uad", "resource_id": "123456asdf23444556", "status": "success"}}, "Create version": {"description": "This API can be used to create a new version. The name of the version must be provided by the user.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.version.create", "sample_data": "workbook.version.create"}, {"param_name": "version_description", "param_desc": "Name of the new version", "sample_data": "version1"}], "response": {"version_description": "version1", "version_number": "1.0", "method": "workbook.version.create", "status": "success"}}, "Revert version": {"description": "This API can be used to revert to an older version.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.version.revert", "sample_data": "workbook.version.revert"}, {"param_name": "version_number", "param_desc": "Version_number of the version to be reverted", "sample_data": "1.0"}], "response": {"method": "workbook.version.revert", "status": "success"}}, "Trash workbook": {"description": "This API allows you to delete workbooks. A maximum of 10 workbooks can be deleted at a time.", "is_docs_api": true, "url": "trash", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "workbook.trash", "sample_data": "workbook.trash"}, {"param_name": "resource_ids", "param_desc": "Resource_ids of the workbooks which have to be trashed. Example: [\"aaaaabbbbbccccdddddeeee\", \"bbbbbccccdddddeeeeaaaaa\"].", "sample_data": "[\"aaaaabbbbbccccdddddeeee\",\"bbbbbccccdddddeeeeaaaaa\"]"}], "response": {"method": "workbook.trash", "status": "success"}}, "Restore workbook": {"description": "This API allows you to restore workbooks from the trash. A maximum of 10 workbooks can be restored from the trash at a time.", "is_docs_api": true, "url": "restore", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "workbook.restore", "sample_data": "workbook.restore"}, {"param_name": "resource_ids", "param_desc": "Resource_ids of the workbooks which have to be restored. Example: [\"aaaaabbbbbccccdddddeeee\", \"bbbbbccccdddddeeeeaaaaa\"].", "sample_data": "[\"aaaaabbbbbccccdddddeeee\",\"bbbbbccccdddddeeeeaaaaa\"]"}], "response": {"method": "workbook.restore", "status": "success"}}, "Delete workbook": {"description": "This API allows you to permanently delete workbooks from the trash that can't be restored. A maximum of 10 workbooks can be deleted at a time.", "is_docs_api": true, "url": "delete", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "workbook.delete", "sample_data": "workbook.delete"}, {"param_name": "resource_ids", "param_desc": "Resource_ids of the workbooks which have to be deleted. Example: [\"aaaaabbbbbccccdddddeeee\", \"bbbbbccccdddddeeeeaaaaa\"].", "sample_data": "[\"aaaaabbbbbccccdddddeeee\",\"bbbbbccccdddddeeeeaaaaa\"]"}], "response": {"method": "workbook.delete", "status": "success"}}, "Publish": {"description": "This API allows you to publish a workbook, worksheet, or a range of a worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "is_docs_api": true, "url": "publish", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "workbook.publish", "sample_data": "workbook.publish"}, {"param_name": "resource_id", "param_desc": "The resource id of the workbook which needs to be published", "sample_data": "aaaaabbbbbccccdddddeeee"}, {"param_name": "publish_type", "param_desc": "workbook | worksheet | range", "sample_data": "workbook"}, {"param_name": "publish_options", "param_type": "json_object", "param_desc": "Optional parameter. A json object of publish options. Example: {\"interaction\":true, \"download\":false, \"hide_formulas\":false, \"hide_gridlines\":true, \"hide_headers\":false, \"hide_formulabar\":true}. <p> The default publish_options for a workbook is {\"interaction\":true, \"download\":false, \"hide_formulas\":true, \"hide_gridlines\":false, \"hide_headers\":false, \"hide_formulabar\":false}, while the default publish_options for a worksheet/range is {\"interaction\":false, \"download\":false, \"hide_formulas\":true, \"hide_gridlines\":false, \"hide_headers\":false, \"hide_formulabar\":false}", "sample_data": {"interaction": true, "download": true, "hide_formulas": true, "hide_gridlines": false}}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet if publish_type is either worksheet, or range", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Required if the publish_type is range. Start row index of the range which needs to be published", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Required if the publish_type is range. Start column index of the range which needs to be published", "sample_data": 3}, {"param_name": "end_row", "param_desc": "Required if the publish_type is range. End row index of the range which needs to be published", "sample_data": 4}, {"param_name": "end_column", "param_desc": "Required if the publish_type is range. End column index of the range which needs to be published", "sample_data": 4}], "response": {"published link": "https://ZohoSheetPublicURL/sheet/publishedsheet/aaaabbbbbbccccccc?type=grid", "embed link": "https://ZohoSheetPublicURL/sheet/publishedsheet/aaaabbbbbbccccccc?type=grid&mode=embed", "downloadable link": "https://ZohoSheetPublicURL/sheet/publishedsheet/aaaabbbbbbccccccc?type=grid&download=csv", "method": "workbook.publish", "status": "success"}}, "Remove publish": {"description": "This API allows you to remove the published resource.", "scope": "ZohoSheet.dataAPI.UPDATE", "is_docs_api": true, "url": "publish", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "workbook.publish.remove", "sample_data": "workbook.publish.remove"}, {"param_name": "resource_id", "param_desc": "The resource id of the workbook which needs to be unpublished", "sample_data": "aaaaabbbbbccccdddddeeee"}, {"param_name": "publish_type", "param_desc": "workbook | worksheet | range", "sample_data": "workbook"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet if publish_type is either worksheet, or range", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Required if the publish_type is range. Start row index of the range which needs to be unpublished", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Required if the publish_type is range. Start column index of the range which needs to be unpublished", "sample_data": 3}, {"param_name": "end_row", "param_desc": "Required if the publish_type is range. End row index of the range which needs to be unpublished", "sample_data": 4}, {"param_name": "end_column", "param_desc": "Required if the publish_type is range. End column index of the range which needs to be unpublished", "sample_data": 4}], "response": {"method": "workbook.publish.remove", "status": "success"}}, "Lock": {"description": "This API can be used to lock a worksheet or a selected range for shared users and external share links. The WorkDrive.files.READ scope must be added if the document contains external share links.", "scope": "ZohoSheet.dataAPI.UPDATE,WorkDrive.files.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "lock", "sample_data": "lock"}, {"param_name": "scope", "param_desc": "worksheet | range", "sample_data": "range"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet that needs to be locked", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Start row index of the range which needs to be locked", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Start column index of the range which needs to be locked", "sample_data": 1}, {"param_name": "end_row", "param_desc": "End row index of the range which needs to be locked", "sample_data": 5}, {"param_name": "end_column", "param_desc": "End column index of the range which needs to be locked", "sample_data": 2}, {"param_name": "user_emails", "param_desc": "Array consisting of email ids of the users for whom the worksheet or range will be locked. At least one of user_emails or external_share_links must be provided. Example: [\"<EMAIL>\", \"<EMAIL>\"]", "sample_data": "[\"<EMAIL>\", \"<EMAIL>\"]"}, {"param_name": "external_share_links", "param_desc": "Optional parameter. Array consisting of external share links for which the worksheet or range will be locked. At least one of user_emails or external_share_links must be provided.<br/>Example: [\"https://WORKDRIVE_EXTERNAL_DOMAIN/external/sheet/&lt;rid&gt;\", \"https://WORKDRIVE_EXTERNAL_DOMAIN/external/sheet/&lt;rid&gt;\"]", "sample_data": "[\"https://WOR<PERSON>DRIVE_EXTERNAL_DOMAIN/external/sheet/dddddeeeeeeeeeffffffff\", \"https://WORKDRIVE_EXTERNAL_DOMAIN/external/sheet/aaaaabbbbbbbbbcccccccc\"]"}], "response": {"method": "lock", "status": "success"}}, "Unlock": {"description": "This API can be used to unlock a worksheet or a selected range for shared users and external share links. The WorkDrive.files.READ scope must be added if the document contains external share links.", "scope": "ZohoSheet.dataAPI.UPDATE,WorkDrive.files.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "unlock", "sample_data": "unlock"}, {"param_name": "scope", "param_desc": "worksheet | range", "sample_data": "range"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet that needs to be locked", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Start row index of the range which needs to be locked", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Start column index of the range which needs to be locked", "sample_data": 1}, {"param_name": "end_row", "param_desc": "End row index of the range which needs to be locked", "sample_data": 5}, {"param_name": "end_column", "param_desc": "End column index of the range which needs to be locked", "sample_data": 2}, {"param_name": "user_emails", "param_desc": "Array consisting of email ids of the users for whom the worksheet or range will be unlocked. At least one of user_emails or external_share_links must be provided. Example: [\"<EMAIL>\", \"<EMAIL>\"]", "sample_data": "[\"<EMAIL>\", \"<EMAIL>\"]"}, {"param_name": "external_share_links", "param_desc": "Optional parameter. Array consisting of external share links for which the worksheet or range will be unlocked. At least one of user_emails or external_share_links must be provided. Example: [\"https://WORKDRIVE_EXTERNAL_DOMAIN/external/sheet/&lt;rid&gt;\", \"https://WORKDRIVE_EXTERNAL_DOMAIN/external/sheet/&lt;rid&gt;\"]", "sample_data": "[\"https://WOR<PERSON>DRIVE_EXTERNAL_DOMAIN/external/sheet/dddddeeeeeeeeeffffffff\", \"https://WORKDRIVE_EXTERNAL_DOMAIN/external/sheet/aaaaabbbbbbbbbcccccccc\"]"}], "response": {"method": "unlock", "status": "success"}}}, "merge": {"sub_list": ["Get merge templates", "Get merge fields", "Get merge jobs", "Get merge job details", "Merge and save", "Merge and email as attachment"], "Get merge templates": {"intro": "Merge Templates in Zoho Sheet allows you to create spreadsheets with dynamic data fields and save the data records as files in Zoho Sheet or share them via email with respective stakeholders. <br/> Please note that \"merge and save API\" and \"merge and email as attachment API\" consume credit points. <br/> <a href=\"https://help.zoho.com/portal/en/kb/sheet/getting-data-analysis-done/articles/using-merge-templates-in-spreadsheets#Convert_to_Merge_Template\" target=\"_blank\">Click here</a> to know more about merge template and credit points.", "description": "This API can be used to list all the merge templates (owned/shared) of the user.", "is_docs_api": true, "url": "mergetemplates", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "mergetemplate.list", "sample_data": "mergetemplate.list"}], "response": {"mergetemplates": [{"created_time": "Friday, 6 April, 2018 4:14:10 PM", "last_modified_time": "Friday, 6 April, 2018 4:14:10 PM", "mergetemplate_name": "Merge template", "mergetemplate_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddeeeee", "resource_id": "aaaaabbbbbccccdddddeeeee", "created_by": "<PERSON>"}, {"created_time": "Friday, 6 April, 2018 4:13:36 PM", "last_modified_time": "Friday, 6 April, 2018 4:13:36 PM", "mergetemplate_name": "Test document", "mergetemplate_url": "https://SHEET_SERVER_URL/sheet/open/aaaaabbbbbccccdddddfffff", "resource_id": "aaaaabbbbbccccdddddfffff", "created_by": "<PERSON>"}], "method": "mergetemplate.list", "status": "success"}}, "Get merge fields": {"description": "This API can be used to list all the merge fields of a workbook.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.mergefield.list", "sample_data": "workbook.mergefield.list"}], "response": {"merge_fields": [{"field_name": "name", "field_id": "12"}, {"field_name": "region", "field_id": "13"}, {"field_name": "units", "field_id": "14"}], "method": "mergefield.list", "status": "success"}}, "Get merge jobs": {"description": "This API can be used to list all the merge jobs performed on a merge template.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.mergejob.list", "sample_data": "workbook.mergejob.list"}], "response": {"merge_jobs": [{"created_time": "Thursday, 2 January, 2024, 8:49:58 PM", "records_count": 3, "merge_type": "ONLY_ONCE", "mergejob_id": "11111111111", "resource_id": "aaaaabbbbbccccdddddeeeee", "assigned_by": "<PERSON>", "merge_action": "CREATE_NEW_FILE", "status": 1}, {"created_time": "Friday, 3 January, 2024, 9:25:19 PM", "records_count": 5, "merge_type": "ONLY_ONCE", "mergejob_id": "11111111115", "resource_id": "aaaaabbbbbccccdddddeeeee", "assigned_by": "<PERSON>", "merge_action": "SEND_MAIL", "status": 1}]}}, "Get merge job details": {"description": "This API can be used to get the details about a single merge job.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "workbook.mergejob.details", "sample_data": "workbook.mergejob.details"}, {"param_name": "mergejob_id", "param_desc": "The merge job id that you receive when you initiate a merge job.", "sample_data": "11111111115"}], "response": {"created_time": "Friday, 3 January, 2024, 9:25:19 PM", "records_count": "1", "merge_type": "ONLY_ONCE", "mergejob_id": "11111111115", "resource_id": "aaaaabbbbbccccdddddeeeee", "mergejob_details": [{"record_index": 1, "file_url": "https://workdrive.zoho.com/file/aaaaabbbbbccccdddddeffff", "file_name": "merge template_1_1", "file_rid": "aaaaabbbbbccccdddddeffff", "status": 1}], "assigned_by": "<PERSON>", "merge_action": "CREATE_NEW_FILE", "status": "COMPLETED"}}, "Merge and save": {"description": "Using this API, you will be able to store the merged document inside Zoho WorkDrive.<br/>The parameter output_settings can contain the following details:<br/> 1. doc_name: (Optional) Name of the new document to be generated after merge. It can contain merge field variable. If not provided, merge template document name will be considered as default.<br/>2.folder_id: (Optional) The Zoho WorkDrive folder id where the newly created documents to be saved. By default, the merged documents will be saved in user's My Folder.<br/>", "is_docs_api": false, "url": "merge", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "merge.save", "sample_data": "merge.save"}, {"param_name": "merge_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"name\":\"<PERSON>\",\"region\":\"South\",\"units\":284},{\"name\":\"<PERSON>\",\"region\":\"East\",\"units\":290}]. \"name\", \"region\", and \"units\" are the merge fields.", "sample_data": [{"name": "<PERSON>", "region": "South", "units": 284}, {"name": "Beth", "region": "East", "units": 290}]}, {"param_name": "output_settings", "param_type": "json_object", "param_desc": "JSON Object which contains the details about new file. Example: {\"doc_name\":\"Document_((name))\",\"folder_id\":\"abcd\"}. In this example ((name)) is a merge field whose value will be taken from merge_data during runtime. With the above example data 2 documents with name Document_Joe and Document_Beth will be craeted.", "sample_data": {"doc_name": "Document_((name))", "folder_id": "abcd"}}], "response": {"method": "merge.save", "mergejob_id": "11111111115", "status": "initiated"}}, "Merge and email as attachment": {"description": "Using this API newly created documents can be send as email attachment, but will not be saved.<br/> The parameter email_settings can contain the following details:<br/>1. subject: (Mandatory) Email subject. This can contain merge field variable.<br/>2. message: (Mandatory) Email message. This can also contain merge field variable.<br/>3. recipient_email: (Mandatory) This can contain merge field variable.<br/>4. output_format: (Mandatory) Possible values are xlsx or pdf.<br/>5. attachment_name: (Optional) Can contain merge field variable. If not provided, merge template document name will be considered as default. <br/>6. send_copy: (optional) The email will be copied to the sender if set to true. The default value is false.", "is_docs_api": false, "url": "merge", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "merge.email.attachment", "sample_data": "merge.email.attachment"}, {"param_name": "merge_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"name\":\"<PERSON>\",\"region\":\"South\",\"units\":284},{\"name\":\"<PERSON>\",\"region\":\"East\",\"units\":290}]. \"name\", \"region\", and \"units\" are the merge fields.", "sample_data": [{"name": "<PERSON>", "region": "South", "units": 284}, {"name": "Beth", "region": "East", "units": 290}]}, {"param_name": "email_settings", "param_type": "json_object", "param_desc": "JSON Object which contains the details about new file. Example: {\"subject\":\"Email Subject\",\"message\":\"Email message\",\"recipient_email\":\"******\",\"attachment_name\":\"Document_((name))\",\"output_format\":\"xlsx\"}.<br/>In the above example ((name)) is a merge field variable whose value will be taken from merge_data during runtime. With the above example data 2 emails will be sent.", "sample_data": {"subject": "Email Subject", "message": "Please find the details in attached file.", "attachment_name": "Document_((name))", "output_format": "xlsx"}}], "response": {"method": "merge.email.attachment", "mergejob_id": "11111111115", "status": "initiated"}}}, "worksheet": {"sub_list": ["List all worksheets", "Create worksheet", "Copy worksheet - same workbook", "Copy worksheet - other workbook", "Rename worksheet", "Delete worksheet"], "List all worksheets": {"description": "This API can be used to list all the worsheets of a particular spreadsheet file, along with worksheet ids.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "worksheet.list", "sample_data": "worksheet.list"}], "response": {"worksheet_names": [{"worksheet_name": "Sheet1", "worksheet_id": "34543455#"}, {"worksheet_name": "Sheet3", "worksheet_id": "32342454#"}, {"worksheet_name": "Sheet5", "worksheet_id": "65446766#"}, {"worksheet_name": "Sheet6", "worksheet_id": "87656789#"}], "method": "worksheet.list", "status": "success"}}, "Create worksheet": {"description": "This API can be used to add a new worksheet into a spreadsheet file. The user may or may not provide the worksheet name. In case the name of the new worksheet is not provided, the API will consider a default worksheet name such as Sheet1, Sheet2, Sheet3, etc.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "worksheet.insert", "sample_data": "worksheet.insert"}, {"param_name": "worksheet_name", "param_desc": "Name of the new worksheet", "sample_data": "Sheet3"}], "response": {"worksheet_names": [{"worksheet_name": "Sheet1", "worksheet_id": "32234543#"}, {"worksheet_name": "Sheet3", "worksheet_id": "32234543#"}], "method": "worksheet.insert", "new_worksheet_name": "Sheet3", "status": "success"}}, "Copy worksheet - same workbook": {"description": "This API can be used to copy or duplicate a worksheet within the workbook", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "worksheet.copy", "sample_data": "worksheet.copy"}, {"param_name": "worksheet_name", "param_desc": "Name of the new worksheet which needs to be copied", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "new_worksheet_name", "param_desc": "Optional parameter. Name of the copied worksheet. By default new worksheet name will be like Sheet1_1, Sheet1_2 etc.", "sample_data": "Sheet1_1"}, {"param_name": "insert_after", "param_desc": "Optional parameter. Id of the worksheet after which copied sheet to be insert. By default it will insert at end.", "sample_data": "3#"}], "response": {"worksheet_names": [{"worksheet_name": "Sheet1", "worksheet_id": "32234543#"}, {"worksheet_name": "Sheet1_1", "worksheet_id": "32234543#"}], "method": "worksheet.insert", "new_worksheet_name": "Sheet1_1", "status": "success"}}, "Copy worksheet - other workbook": {"description": "This API can be used to copy a worksheet from another workbook", "scope": "ZohoSheet.dataAPI.UPDATE,ZohoSheet.dataAPI.READ", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "worksheet.copy.otherdoc", "sample_data": "worksheet.copy.otherdoc"}, {"param_name": "source_resource_id", "param_desc": "Optional parameter. Required if you copy the worksheet from another document.", "sample_data": "aaaaabbbbbccccdddddfffff"}, {"param_name": "source_worksheet_name", "param_desc": "Name of the new worksheet which needs to be copied", "sample_data": "Sheet1"}, {"param_name": "source_worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}], "response": {"worksheet_names": [{"worksheet_name": "Sheet1", "worksheet_id": "0#"}, {"worksheet_name": "Sheet1_1", "worksheet_id": "1#"}], "method": "worksheet.copy.otherdoc", "status": "success"}}, "Rename worksheet": {"description": "This API can be used to rename an existing worksheet. The worksheet id will remain the same after naming the worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "worksheet.rename", "sample_data": "worksheet.rename"}, {"param_name": "old_name", "param_desc": "Name of the existing worksheet", "sample_data": "awd"}, {"param_name": "new_name", "param_desc": "New name that needs to be set", "sample_data": "asd"}], "response": {"worksheet_names": [{"worksheet_name": "asd", "worksheet_id": "32234324#"}], "method": "worksheet.rename", "status": "success"}}, "Delete worksheet": {"description": "This API can be used to delete an existing worksheet of the spreadsheet file. The spreadsheet must have more than one worksheet. In case the spreadsheet contains only a single worksheet, the API will throw an error.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "worksheet.delete", "sample_data": "worksheet.delete"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet that needs to be deleted", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}], "response": {"worksheet_names": [{"worksheet_name": "Sheet1", "worksheet_id": "32234324#"}], "method": "worksheet.delete", "status": "success"}}}, "tabular": {"sub_list": ["List all tables", "Create table", "Remove table", "Rename headers of table", "Fetch records from table", "Add records to table", "Update records in table", "Delete records from table", "Insert columns to table", "Delete columns from table", "Fetch records from worksheet", "Add records to worksheet", "Update records in worksheet", "Delete records from worksheet", "Insert columns to records"], "List all tables": {"description": "This API can be used to list all the tables in the workbook along with the table ids and range.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "table.list", "sample_data": "table.list"}], "response": {"tables": [{"table_name": "Table1", "table_id": 1, "start_row": 1, "start_column": 1, "end_row": 10, "end_column": 5}, {"table_name": "Table2", "table_id": 2, "start_row": 5, "start_column": 8, "end_row": 12, "end_column": 13}], "method": "table.list", "status": "success"}}, "Create table": {"description": "This API can be used to create a new table. The table styles and properties can also be specified. The templates available for table_style are LIGHT1, LIGHT2, LIGHT3, MEDIUM1, MEDIUM2, MEDIUM3, MEDIUM4, MEDIUM5, DARK1.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "table.create", "sample_data": "table.create"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "start row index of the table", "sample_data": 2}, {"param_name": "start_column", "param_desc": "start column index of the table", "sample_data": 2}, {"param_name": "end_row", "param_desc": "end row index of the table", "sample_data": 9}, {"param_name": "end_column", "param_desc": "end column index of the table", "sample_data": 6}, {"param_name": "contains_header", "param_desc": "Optional parameter. The default value is true. If set to false, the header row will be inserted at the top as an additional row to the selected range.", "sample_data": "true"}, {"param_name": "header_names", "param_type": "array", "param_desc": "Optional parameter. Array of header values for the created table. The header_names must be unique. For example : [\"country\",\"region\"]. The length of header_names and column length of the table must be equal.", "sample_data": "[\"Phone\",\"Email\"]"}, {"param_name": "table_style", "param_type": "json_object", "param_desc": "Optional parameter. A json array of styles and properties for table. The template and color values correspond to the options provided in the spreadsheet. Instead of the predefined color options (Accent1 to Accent6), hex color codes can also be used. Example: {\"template\":\"light1\", \"color\":\"#FF5733\", \"properties\" : {\"first_column\":true, \"last_column\":true, \"banded_rows\":true, \"banded_columns\":false, \"total_row\":false}}. The default table_styles for a table is {\"template\":\"Medium1\", \"color\":\"Accent1\", \"properties\": {\"first_column\":false, \"last_column\":false, \"banded_rows\":true, \"banded_columns\":false, \"total_row\":false}}", "sample_data": {"template": "light1", "color": "Accent2", "properties": {"first_column": true, "last_column": true, "banded_rows": true, "banded_columns": false, "total_row": false}}}], "response": {"table_name": "Table1", "table_id": 1, "method": "table.create", "status": "success"}}, "Remove table": {"description": "This API can be used to remove a table. The formats can also be cleared along with the table.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "table.remove", "sample_data": "table.remove"}, {"param_name": "table_name", "param_desc": "Name of the table which needs to be removed", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": 1}, {"param_name": "clear_format", "param_desc": "Optional parameter. The default value is false. If set to true, all table formats will be cleared along with the table.", "sample_data": "true"}], "response": {"method": "table.remove", "status": "success"}}, "Rename headers of table": {"description": "This API can be used to rename the header values of a table.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "table.header.rename", "sample_data": "table.header.rename"}, {"param_name": "table_name", "param_desc": "Name of the table which needs to be removed", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": 1}, {"param_name": "data", "param_type": "json_array", "param_desc": "The headers that need to be updated. The key \"old_name\" denotes the name of the existing header and \"new_name\" denotes the new header name that needs to be set. Example: [{\"old_name\":\"Name\",\"new_name\":\"First Name\"},{\"old_name\":\"Units\",\"new_name\":\"Count\"}]", "sample_data": [{"old_name": "Name", "new_name": "First Name"}, {"old_name": "Units", "new_name": "Count"}]}], "response": {"method": "table.header.rename", "status": "success"}}, "Fetch records from table": {"description": "This API can be used to fetch records of a table after filtering the records based on some criteria. If no criteria is mentioned it will return all the records. <br/><br/>The criteria_json should be in the following format. <br/>[{\"key\":\"&lt;Column header name&gt;\",\"operator\":\"&lt;operator&gt;\",\"matcher\":\"&lt;Matched String&gt;\",\"type\":\"String | Date | Number\"}]<br/>If there are more than 1 criteria, criteria_pattern should be used.<br/><br/>Supported operators are : EQUALS | NOT_EQUALS | CONTAINS | NOT_CONTAINS | GREATER_THAN | LESS_THAN | IS_EMPTY | IS_NOT_EMPTY | IN | NOT_IN<br/>The key \"type\" is optional and the default value is String. Supported types are: String | Date | Number.<br/>Please note that only EQUALS, NOT_EQUALS, GREATER_THAN, and LESS_THAN are supported for date and number type data. CONTAINS, NOT_CONTAINS, IN, and NOT_IN works for String type data only.<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria_json : <br/><ul style=\"list-style-type: none;\"><li>1. [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"}]</li><li>2.criteria_json = [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}] <br/> criteria_pattern = 1 AND 2<li>3.criteria_json = [{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pen\"},{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pencil\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":40,\"type\":\"Number\"}] <br/> criteria_pattern = (1 OR 2) AND 3</li></ul>Maximum of 5 criteria can be used.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "table.records.fetch", "sample_data": "table.records.fetch"}, {"param_name": "table_name", "param_desc": "Name of the table whose records needs to be fetched", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": "2"}, {"param_name": "criteria_json", "param_desc": "Optional parameter. Can be used to filter records.", "sample_data": "[{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\",\"type\":\"String\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}]"}, {"param_name": "criteria_pattern", "param_desc": "Required when more than 1 criteria is available under criteria_json", "sample_data": "1 AND 2"}, {"param_name": "column_names", "param_desc": "Optional parameter. Can be used to read particular columns data. By default all the columns data will be available in response. Multiple column names must be separated by comma.", "sample_data": "Month,Amount"}, {"param_name": "render_option", "param_desc": "Optional parameter. It defines how the value should be rendered. Possible options are formatted, unformatted, and formula.", "sample_data": "formatted"}, {"param_name": "count", "param_desc": "Optional parameter. It denotes the number of records.", "sample_data": 2}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}], "response": {"records": [{"Month": "March", "Source": "<PERSON>", "Item": "Pen", "Amount": 60, "row_index": 1}, {"Month": "April", "Source": "<PERSON>", "Item": "Pencil", "Amount": 40, "row_index": 2}], "method": "table.records.fetch", "records_count": 21, "status": "success"}}, "Add records to table": {"description": "This API can be used to add records to a table. The records will be in JSON formatted data and will be appended after the last row of the table by default. The key of JSON Object will represent the column header of the table.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "60", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "table.records.add", "sample_data": "table.records.add"}, {"param_name": "table_name", "param_desc": "Name of the table", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": "2"}, {"param_name": "insert_at_top", "param_desc": "Optional parameter. Can be set as true to add records at the top. By default it is false.", "sample_data": "2"}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284},{\"Name\":\"Beth\",\"Region\":\"East\",\"Units\":290}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284}, {"Name": "Beth", "Region": "East", "Units": 290}]}], "response": {"method": "table.records.add", "status": "success", "sheet_name": "Sheet1", "start_row": 10, "start_column": 1, "end_row": 11, "end_column": 3}}, "Update records in table": {"description": "This API is used to update records in a table based on some criteria. <br/>The criteria_json should be in the following format. <br/>[{\"key\":\"&lt;Column header name&gt;\",\"operator\":\"&lt;operator&gt;\",\"matcher\":\"&lt;Matched String&gt;\",\"type\":\"String | Date | Number\"}]<br/>If there are more than 1 criteria, criteria_pattern should be used.<br/><br/>Supported operators are : EQUALS | NOT_EQUALS | CONTAINS | NOT_CONTAINS | GREATER_THAN | LESS_THAN | IS_EMPTY | IS_NOT_EMPTY | IN | NOT_IN<br/>The key \"type\" is optional and the default value is String. Supported types are: String | Date | Number.<br/>Please note that only EQUALS, NOT_EQUALS, GREATER_THAN, and LESS_THAN are supported for date and number type data. CONTAINS, NOT_CONTAINS, IN, and NOT_IN works for String type data only.<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria_json : <br/><ul style=\"list-style-type: none;\"><li>1. [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"}]</li><li>2.criteria_json = [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}] <br/> criteria_pattern = 1 AND 2<li>3.criteria_json = [{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pen\"},{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pencil\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":40,\"type\":\"Number\"}] <br/> criteria_pattern = (1 OR 2) AND 3</li></ul>Maximum of 5 criteria can be used.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "60", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "table.records.update", "sample_data": "table.records.update"}, {"param_name": "table_name", "param_desc": "Name of the table whose records needs to be updated", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": "2"}, {"param_name": "criteria_json", "param_desc": "Optional parameter. Can be used to filter records.", "sample_data": "[{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\",\"type\":\"String\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}]"}, {"param_name": "criteria_pattern", "param_desc": "Required when more than 1 criteria is available under criteria_json", "sample_data": "1 AND 2"}, {"param_name": "first_match_only", "param_desc": "Optional parameter. If true and if there are multiple records on the specified criteria, records will be updated for first match alone. Otherwise, all the matched records will be updated.", "sample_data": false}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}, {"param_name": "data", "param_type": "json_object", "param_desc": "The JSON data that needs to be updated. Example:{\"Month\":\"May\",\"Amount\":50}", "sample_data": {"Month": "May", "Amount": 50}}], "response": {"no_of_affected_rows": 2, "method": "table.records.update", "status": "success"}}, "Delete records from table": {"description": "This API is used to delete records from a table based on some criteria. The criteria_json should be in the following format. <br/>[{\"key\":\"&lt;Column header name&gt;\",\"operator\":\"&lt;operator&gt;\",\"matcher\":\"&lt;Matched String&gt;\",\"type\":\"String | Date | Number\"}]<br/>If there are more than 1 criteria, criteria_pattern should be used.<br/><br/>Supported operators are : EQUALS | NOT_EQUALS | CONTAINS | NOT_CONTAINS | GREATER_THAN | LESS_THAN | IS_EMPTY | IS_NOT_EMPTY | IN | NOT_IN<br/>The key \"type\" is optional and the default value is String. Supported types are: String | Date | Number.<br/>Please note that only EQUALS, NOT_EQUALS, GREATER_THAN, and LESS_THAN are supported for date and number type data. CONTAINS, NOT_CONTAINS, IN, and NOT_IN works for String type data only.<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria_json : <br/><ul style=\"list-style-type: none;\"><li>1. [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"}]</li><li>2.criteria_json = [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}] <br/> criteria_pattern = 1 AND 2<li>3.criteria_json = [{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pen\"},{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pencil\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":40,\"type\":\"Number\"}] <br/> criteria_pattern = (1 OR 2) AND 3</li></ul>Maximum of 5 criteria can be used.\",", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "60", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "table.records.delete", "sample_data": "table.records.delete"}, {"param_name": "table_name", "param_desc": "Name of the table whose records needs to be deleted", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": "2"}, {"param_name": "criteria_json", "param_desc": "Optional parameter. Can be used to filter records.", "sample_data": "[{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\",\"type\":\"String\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}]"}, {"param_name": "criteria_pattern", "param_desc": "Required when more than 1 criteria is available under criteria_json", "sample_data": "1 AND 2"}, {"param_name": "first_match_only", "param_desc": "Optional parameter. If true and if there are multiple records on the specified criteria, records will be updated for first match alone. Otherwise, all the matched records will be updated.", "sample_data": false}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}], "response": {"no_of_rows_deleted": 5, "no_of_rows_remaining": 8, "method": "table.records.delete", "status": "success"}}, "Insert columns to table": {"description": "This API can be used to append one or more columns with headers at the end of a table. The column headers will be added to the header row.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "table.columns.insert", "sample_data": "table.columns.insert"}, {"param_name": "table_name", "param_desc": "Name of the table", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": "2"}, {"param_name": "column_names", "param_type": "array", "param_desc": "Array of header values of the newly added columns. For example : [\"country\",\"region\"] will insert two columns with the headers country and region.", "sample_data": "[\"Phone\",\"Email\"]"}, {"param_name": "insert_column_after", "param_desc": "Optional parameter. Column after which the columns need to be inserted. By default it will insert at end.", "sample_data": "First Name"}], "response": {"method": "table.columns.insert", "status": "success"}}, "Delete columns from table": {"description": "This API is used to delete multiple columns from a table.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "table.columns.delete", "sample_data": "table.columns.delete"}, {"param_name": "table_name", "param_desc": "Name of the table", "sample_data": "Table1"}, {"param_name": "table_id", "param_desc": "Alternatively table_id can be used instead of table_name", "sample_data": "2"}, {"param_name": "column_names", "param_desc": "Array of column header values that need to be deleted. For example : [\"country\",\"region\"] will remove the two headers.", "sample_data": "[\"Phone\",\"Email\"]"}], "response": {"method": "table.columns.delete", "status": "success"}}, "Fetch records from worksheet": {"intro": "Tabular Data APIs are useful when you want to use Zoho Sheet as a data store. You could add records as well as fetch/update/delete records based on a criteria. The first row of the worksheet is considered as the header by default.", "description": "This API is used to fetch records of a worksheet after filtering the records based on some criteria. If no criteria is mentioned it will return all the records. The criteria should be in the following format. <br/>&lt;Column header name&gt;(=, !=, >, <, contains)&lt;matcher&gt;<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria : <br/><ul style=\"list-style-type: none;\"><li>1. \"Month\"=\"March\"</li><li>2. \"Item\" contains \"Stand\"</li><li>3. \"Month\"=\"March\" and \"Amount\">50</li><li>4. (\"Month\"=\"March\" or \"Month\"=\"April\") and \"Amount\">30</li></ul>Maximum of 5 nested criteria can be used.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "worksheet.records.fetch", "sample_data": "worksheet.records.fetch"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose records needs to be fetched", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. By default, first row of the worksheet is considered as header row. This can be used if tabular data starts from any row other than the first row.", "sample_data": 1}, {"param_name": "criteria", "param_desc": "Optional parameter. Can be used to filter records.", "sample_data": "(\"Month\"=\"March\" or \"Month\"=\"April\") and \"Amount\">30"}, {"param_name": "column_names", "param_desc": "Optional parameter. Can be used to read particular column's data. By default all the column data will be available in response. Multiple column names must be separated by comma.", "sample_data": "Month,Amount"}, {"param_name": "render_option", "param_desc": "Optional parameter. It defines how the value should be rendered. Possible options are formatted, unformatted, and formula.", "sample_data": "formatted"}, {"param_name": "records_start_index", "param_desc": "Optional parameter. This parameter can be used to get a few resources if there are too many.", "sample_data": 1}, {"param_name": "count", "param_desc": "Optional parameter. It denotes the number of records.", "sample_data": 2}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}], "response": {"records": [{"Month": "March", "Source": "<PERSON>", "Item": "Pen", "Amount": 60, "row_index": 1}, {"Month": "April", "Source": "<PERSON>", "Item": "Pencil", "Amount": 40, "row_index": 2}], "method": "worksheet.records.fetch", "records_count": 21, "records_start_index": 1, "records_end_index": 3, "status": "success"}}, "Add records to worksheet": {"description": "This API can be used to add records to a worksheet. The records will be in JSON formatted data and will be appended after the last used row of the worksheet. The key of JSON Object will represent the column header of the worksheet.<br/><br/>A maximum of 1000 rows can be added in a single API call. For more than 1000 rows, you can make multiple API calls.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.records.add", "sample_data": "worksheet.records.add"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. Default value is 1. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": 1}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284},{\"Name\":\"Beth\",\"Region\":\"East\",\"Units\":290}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284}, {"Name": "Beth", "Region": "East", "Units": 290}]}], "response": {"method": "worksheet.records.add", "status": "success", "sheet_name": "Sheet1", "start_row": 10, "start_column": 1, "end_row": 11, "end_column": 3}}, "Insert columns to records": {"description": "This API can be used to insert one or more column with headers. The Column headers will be added in the Header row.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "records.columns.insert", "sample_data": "records.columns.insert"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. Default value is 1. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": ""}, {"param_name": "insert_column_after", "param_desc": "Column name after which new column to be inserted. By default new columns will be appended at the end of the table.", "sample_data": "Region"}, {"param_name": "column_names", "param_type": "array", "param_desc": "Array of header values of the newly added columns. For example : [\"country\",\"region\"] will insert two columns with header country and region.", "sample_data": "[\"Phone\",\"Email\"]"}], "response": {"method": "records.columns.insert", "status": "success"}}, "Update records in worksheet": {"description": "This API is used to update records based on some criteria. The criteria should be in the following format. <br/>&lt;Column header name&gt;(=, !=, >, <, contains)&lt;matcher&gt;<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria : <br/><ul style=\"list-style-type: none;\"><li>1. \"Month\"=\"March\"</li><li>2. \"Item\" contains \"Stand\"</li><li>3. \"Month\"=\"March\" and \"Amount\">50</li><li>4. (\"Month\"=\"March\" or \"Month\"=\"April\") and \"Amount\">30</li></ul>Maximum of 5 nested criteria can be used.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.records.update", "sample_data": "worksheet.records.update"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose records needs to be update", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. By default, first row of the worksheet is considered as header row. This can be used if tabular data starts from any row other than the first row.", "sample_data": 1}, {"param_name": "criteria", "param_desc": "Optional parameter. If criteria is not set all available rows will get updated. Mention the criteria as described above.", "sample_data": "\"Month\"=\"March\""}, {"param_name": "first_match_only", "param_desc": "Optional parameter. If true and if there are multiple records on the specified criteria, records will be updated for first match alone. Otherwise, all the matched records will be updated.", "sample_data": false}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}, {"param_name": "data", "param_type": "json_object", "param_desc": "The JSON data that needs to be updated. Example:{\"Month\":\"May\",\"Amount\":50}", "sample_data": {"Month": "May", "Amount": 50}}], "response": {"no_of_affected_rows": 2, "method": "worksheet.records.update", "status": "success"}}, "Delete records from worksheet": {"description": "This API is used to delete records based on some criteria. The API erases only data and does not delete the rows by default. If you want to delete the rows completely set the value parameter \"delete_rows\"=true. <br/>The API may not delete all the records/rows in case there are large number of discontinuous rows matches the criteria. The API response will contain the key \"no_of_rows_remaining\" if all the rows are not deleteted. You may need to call the API more than once if all the rows are not deleted.<br/>The criteria should be in the following format. <br/>&lt;Column header name&gt;(=, !=, >, <, contains)&lt;matcher&gt;<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria : <br/><ul style=\"list-style-type: none;\"><li>1. \"Month\"=\"March\"</li><li>2. \"Item\" contains \"Stand\"</li><li>3. \"Month\"=\"March\" and \"Amount\">50</li><li>4. (\"Month\"=\"March\" or \"Month\"=\"April\") and \"Amount\">30</li></ul>Maximum of 5 nested criteria can be used.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "worksheet.records.delete", "sample_data": "worksheet.records.delete"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose records needs to be deleted", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. By default, first row of the worksheet is considered as header row. This can be used if tabular data starts from any row other than the first row.", "sample_data": 1}, {"param_name": "criteria", "param_desc": "Mention the criteria as described above.", "sample_data": "\"Month\"=\"March\" and \"Amount\">50"}, {"param_name": "row_array", "param_desc": "Array of row indexs, which needs to be deleted", "sample_data": "[1,2,3,4,5]"}, {"param_name": "first_match_only", "param_desc": "Optional parameter. If true and if there are multiple records on the specified criteria, records will be deleted for first match alone. Otherwise, all the matched records will be deleted. This parameter will be ignored if criteria is not mentioned.", "sample_data": false}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": false}, {"param_name": "delete_rows", "param_desc": "Optional parameter and by default it is false. If true it will delete the rows completely, otherwise the records are only erased by default.", "sample_data": true}], "response": {"no_of_rows_deleted": 5, "no_of_rows_remaining": 0, "method": "worksheet.records.delete", "status": "success"}}}, "content": {"sub_list": ["Get content of cell", "Get content of range", "Get content of named range", "Get content of worksheet", "Get used area", "Set content to cell", "Set content to multiple cells", "Set content to row", "Set content to range", "Append rows with CSV data", "Append rows with JSON data", "Update rows with JSON data", "Insert row with JSON data", "Clear contents of range", "Clear range", "Clear filters", "Find", "Find and replace", "Recalculate"], "Get content of cell": {"description": "This API can be used to get the content and note of a particular cell. Style or formats applied on the cell will not be provided.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "cell.content.get", "sample_data": "cell.content.get"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Row index of the cell", "sample_data": 2}, {"param_name": "column", "param_desc": "Column index of the cell", "sample_data": 2}], "response": {"row_index": 2, "comment": "Hello World", "column_index": 2, "content": "4", "method": "cell.content.get", "status": "success"}}, "Get content of range": {"description": "This API can be used to get the contents of a range. Style or formats applied on the cells will not be provided.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "range.content.get", "sample_data": "range.content.get"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Start row index of the range", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Start column index of the range", "sample_data": 3}, {"param_name": "end_row", "param_desc": "End row index of the range", "sample_data": 4}, {"param_name": "end_column", "param_desc": "End column index of the range", "sample_data": 4}], "response": {"range_details": [{"row_index": 2, "row_details": [{"column_index": 3, "content": "d"}]}, {"row_index": 4, "row_details": [{"column_index": 3, "content": "d"}, {"column_index": 4, "content": "2"}]}], "method": "range.content.get", "status": "success"}}, "Get content of named range": {"description": "This API can be used to get the contents of a named range. Style or formats applied on the cells will not be provided.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "namedrange.content.get", "sample_data": "namedrange.content.get"}, {"param_name": "name_of_range", "param_desc": "name of the named range", "sample_data": "examplerange"}], "response": {"range_details": [{"row_index": 2, "row_details": [{"column_index": 3, "content": "d"}]}, {"row_index": 4, "row_details": [{"column_index": 3, "content": "d"}, {"column_index": 4, "content": "2"}]}], "method": "namedrange.content.get", "status": "success"}}, "Get content of worksheet": {"description": "This API is used to get the complete contents of a worksheet. Style or formats applied on the cells will not be provided.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "worksheet.content.get", "sample_data": "worksheet.content.get"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose content needs to be read", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Optional Parameter. This can be used if the user wants to read sheet data from a specific row index", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Optional paramter. This can be used if the user wants data starting from a particular column", "sample_data": 3}, {"param_name": "end_row", "param_desc": "Optional parameter. This can be used if the user wants data upto a specific row", "sample_data": 4}, {"param_name": "end_column", "param_desc": "Optional parameter. This can be used if the user wants data upto a specific column", "sample_data": 4}, {"param_name": "visible_rows_only", "param_desc": "Optional parameter. The default value is false. If set to true, hidden rows will be skipped.", "sample_data": ""}, {"param_name": "visible_columns_only", "param_desc": "Optional parameter. The default value is false. If set to true, hidden columns will be skipped.", "sample_data": true}, {"param_name": "response_type", "param_desc": "Optional Parameter. This can be used if the user wants the cell content as an array instead of default. Supported response type are 'array' and 'default'.", "sample_data": "default"}, {"param_name": "major_dimension", "param_desc": "This parameter is required only if response_type is array. Supported major_dimensions are 'rows' and 'columns'.", "sample_data": ""}], "response": {"range_details": [{"row_index": 2, "row_details": [{"column_index": 3, "content": "d"}]}, {"row_index": 4, "row_details": [{"column_index": 3, "content": "d"}, {"column_index": 4, "content": "2"}]}], "method": "worksheet.content.get", "used_row": 27, "used_column": 9, "status": "success"}}, "Get used area": {"description": "This API can be used to get the count of the used area of a particular spreadsheet. The used area indicates the row upto which the spreadsheet contains data, and upto which column the spreadsheet contains data. It does not consider cells, with format or styles, without data.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "worksheet.usedarea", "sample_data": "worksheet.usedarea"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet2"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": "2#"}], "response": {"used_row_index": 10, "method": "worksheet.usedarea", "used_column_index": 3, "worksheet_name": "Sheet2", "status": "success"}}, "Set content to cell": {"description": "This API can be used to add content to particular cell in a specified worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "cell.content.set", "sample_data": "cell.content.set"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Row index of the cell whose content needed an update", "sample_data": 4}, {"param_name": "column", "param_desc": "Column index of the cell whose content needed an update", "sample_data": 4}, {"param_name": "content", "param_desc": "The content that needed to be set", "sample_data": "asd"}], "response": {"method": "cell.content.set", "cell": {"row_index": 4, "formula": "asd", "cell_value": "asd", "column_index": 4}, "status": "success"}}, "Set content to multiple cells": {"description": "This API can be used to add content to multiple discontinuous cells. You can update upto 50 cells using a single API call.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "cells.content.set", "sample_data": "cells.content.set"}, {"param_name": "data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"worksheet_id\":\"0#\",\"content\":\"asd\",\"row\":23,\"column\":5},{\"worksheet_id\":\"1#\",\"content\":\"new\",\"row\":10,\"column\":46}].", "sample_data": [{"worksheet_id": "0#", "content": "asd", "row": 23, "column": 5}, {"worksheet_id": "1#", "content": "new", "row": 10, "column": 46}]}], "response": {"method": "cells.content.set", "status": "success"}}, "Set content to fields and create new worksheets": {"description": "This API can be used to set content in all the cells represented by a field and create new worksheets", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "fields.create.worksheets"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name"}, {"param_name": "fields_data", "param_desc": "JSONArray with Objects containing <<Field_name>> in keys and the contents that needed to be set in values.Eg [{\"name\":\"giftson\",\"mobile\":123456789},{\"name\":\"samuel\",\"mobile\":987654321}]"}, {"param_name": "new_worksheet_name", "param_desc": "Optional parameter. If the user wants a specific name for all the individual worksheets."}], "response": {"method": "fields.create.worksheets", "status": "success"}}, "Set content to fields and create new workbooks": {"description": "This API can be used to set content in all the cells represented by a field and create new workbooks", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "fields.create.workbooks"}, {"param_name": "fields_data", "param_desc": "JSONArray with Objects containing <<Field_name>> in keys and the contents that needed to be set in values.Eg [{\"name\":\"giftson\",\"mobile\":123456789},{\"name\":\"samuel\",\"mobile\":987654321}]"}, {"param_name": "new_workbook_name", "param_desc": "Optional parameter. If the user wants a specific name for the new workbooks."}], "response": {"method": "fields.create.workbooks", "status": "success"}}, "Set content to fields and create new pdfs": {"description": "This API can be used to set content in all the cells represented by a field and create new pdfs", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "fields.create.pdfs"}, {"param_name": "fields_data", "param_desc": "JSONArray with Objects containing <<Field_name>> in keys and the contents that needed to be set in values.Eg [{\"name\":\"giftson\",\"mobile\":123456789},{\"name\":\"samuel\",\"mobile\":987654321}]"}, {"param_name": "pdf_file_name", "param_desc": "Optional parameter. If the user wants a specific name for the new pdf files."}], "response": {"method": "fields.create.pdfs", "status": "success"}}, "Set content to fields and email as attachment": {"description": "This API can be used to set content in all the cells represented by a field and email and new file as attachemnt", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "fields.mail.attachment"}, {"param_name": "fields_data", "param_desc": "JSONArray with Objects containing <<Field_name>> in keys and the contents that needed to be set in values.Eg [{\"name\":\"giftson\",\"mobile\":123456789},{\"name\":\"samuel\",\"mobile\":987654321}]"}, {"param_name": "file_format", "param_desc": "Optional parameter. If the user wants a specific file format. Available file formats are xlsx, xls, csv, ods, and pdf. xlsx is the defalut file format."}, {"param_name": "recipients", "param_desc": "Email ids to whom the mail to be send."}, {"param_name": "subject", "param_desc": "Subject of the email attachment."}, {"param_name": "message", "param_desc": "Message or content of the email attachment."}, {"param_name": "send_me_a_copy", "param_desc": "Optional parameter. Default value is false. Set it as true if you want to receive a copy of the email."}], "response": {"method": "fields.create.pdfs", "status": "success"}}, "Set content to row": {"description": "This API can be used to update a few discrete columns of a particular row. Here, the parameter \"columns\" represents an array of column indices which needs to be updated. The length of the column array and the data array must be equal.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "row.content.set", "sample_data": "row.content.set"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Index postion of the row whose data needs to be updated", "sample_data": 3}, {"param_name": "column_array", "param_desc": "Array of column indexs", "sample_data": "[1,2,3,4]"}, {"param_name": "data_array", "param_type": "array", "param_desc": "Array of data", "sample_data": "[\"john\",\"joe\"]"}], "response": {"method": "row.content.set", "status": "success"}}, "Set content to range": {"description": "This API can be used to insert CSV data to a worksheet, at a specified cell reference. This API will overwrite any existing content with the inserted CSV data. Empty value in CSV data will not be considered if the parameter ignore_empty is set to true.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "worksheet.csvdata.set", "sample_data": "worksheet.csvdata.set"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Row index of the cell from which CSV data will start", "sample_data": 3}, {"param_name": "column", "param_desc": "Column index of the cell from which CSV data will start", "sample_data": 1}, {"param_name": "ignore_empty", "param_desc": "If set to true empty value in CSV data will be ignored", "sample_data": "true"}, {"param_name": "data", "param_type": "csv_data", "param_desc": "CSV data", "sample_data": "1,2,,\"<PERSON>\""}], "response": {"method": "worksheet.csvdata.set", "status": "success"}}, "Append rows with CSV data": {"description": "This API can be used to insert CSV data to a worksheet. The CSV data will be appended after the last used row of the worksheet. In CSV data every comma will move the data to the next cell and every new line character will move the data to the next row.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "worksheet.csvdata.append", "sample_data": "worksheet.csvdata.append"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "data", "param_desc": "CSV data", "sample_data": "1,2,3"}], "response": {"method": "worksheet.csvdata.append", "status": "success", "sheet_name": "Sheet1", "start_row": 10, "start_column": 1, "end_row": 10, "end_column": 3}}, "Append rows with JSON data": {"description": "This API can be used to insert data to a worksheet. The JSON data will be appended after the last used row of the worksheet. The key of JSON Object will represent the column header of the worksheet.<br/><br/>A maximum of 1000 rows can be added in a single API call. For more than 1000 rows, you can make multiple API calls.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.jsondata.append", "sample_data": "worksheet.jsondata.append"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. Default value is 1. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": 1}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284},{\"Name\":\"Beth\",\"Region\":\"East\",\"Units\":290}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284}, {"Name": "Beth", "Region": "East", "Units": 290}]}], "response": {"method": "worksheet.jsondata.append", "status": "success", "sheet_name": "Sheet1", "start_row": 10, "start_column": 1, "end_row": 11, "end_column": 3}}, "Update rows with JSON data": {"description": "This API can be used to update multiple discontinuous rows if the row indexs are known. The key of JSON Object will represent the column header of the worksheet. The JSON Object must contain the row_index.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.jsondata.set", "sample_data": "worksheet.jsondata.set"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. Default value is 1. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": 1}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284, \"row_index\":4},{\"Name\":\"Beth\",\"Region\":\"East\",\"Units\":290, \"row_index\":4}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284, "row_index": 4}, {"Name": "Beth", "Region": "East", "Units": 290, "row_index": 4}]}], "response": {"method": "worksheet.jsondata.set", "status": "success"}}, "Insert row with JSON data": {"description": "This API can be used to insert a new row with the specified data. The key of JSON Object will represent the column header of the worksheet. Cells of the newly inserted row will have the format of the respective cells of the previous row.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.jsondata.insert", "sample_data": "worksheet.jsondata.insert"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Row index at which new row to be inserted", "sample_data": 4}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284}]}, {"param_name": "header_row", "param_desc": "Optional parameter. By default, first row of the worksheet is considered as header row. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": 1}], "response": {"method": "worksheet.jsondata.insert", "status": "success"}}, "Clear contents of range": {"description": "This API can be used to clear only the contents of a particular range. Any style or format applied to that range will remain intact.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "range.content.clear", "sample_data": "range.content.clear"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose range needs to be cleared", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Start row index of the range which needs to be cleared", "sample_data": 3}, {"param_name": "start_column", "param_desc": "Start column index of the range which needs to be cleared", "sample_data": 1}, {"param_name": "end_row", "param_desc": "End row index of the range which needs to be cleared", "sample_data": 5}, {"param_name": "end_column", "param_desc": "End column index of the range which needs to be cleared", "sample_data": 3}], "response": {"method": "range.content.clear", "status": "success"}}, "Clear range": {"description": "This API can be used to clear everything including style, format, and content of a range.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "range.clear", "sample_data": "range.clear"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose range needs to be cleared", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Start row index of the range which needs to be cleared", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Start column index of the range which needs to be cleared", "sample_data": 1}, {"param_name": "end_row", "param_desc": "End row index of the range which needs to be cleared", "sample_data": 5}, {"param_name": "end_column", "param_desc": "End column index of the range which needs to be cleared", "sample_data": 2}], "response": {"method": "range.clear", "status": "success"}}, "Clear filters": {"description": "This API allows you to reset or clear all applied filters in a worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "worksheet.filter.clear", "sample_data": "worksheet.filter.clear"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}], "response": {"method": "worksheet.filter.clear", "status": "success"}}, "Find": {"description": "This API is used to search for a particular string in a workbook, or in a worksheet, or in a row, or in a column", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "find", "sample_data": "find"}, {"param_name": "search", "param_desc": "The string that needs to be searched", "sample_data": "jack"}, {"param_name": "scope", "param_desc": "workbook | worksheet | row | column", "sample_data": "workbook"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet if scope is either worksheet, or row, or column", "sample_data": ""}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Required if the scope is row", "sample_data": ""}, {"param_name": "column", "param_desc": "Required if the scope is column", "sample_data": ""}, {"param_name": "is_case_sensitive", "param_desc": "If set to true upper case and lower case characters will be different during search. By default it is false", "sample_data": "true"}, {"param_name": "is_exact_match", "param_desc": "If set to true the search will match with the full content of the cell.", "sample_data": "true"}], "response": {"cells": [{"row_index": 1, "column_index": 1, "content": "jack", "worksheet_name": "test2", "worksheet_id": "34543455#"}, {"row_index": 1, "column_index": 4, "content": "jack", "worksheet_name": "test2", "worksheet_id": "32224456#"}, {"row_index": 10, "column_index": 2, "content": "jack", "worksheet_name": "Sheet2", "worksheet_id": "78987766#"}], "method": "find", "matches_found": 4, "status": "success"}}, "Find and replace": {"description": "This API can be used to search for a specified term in a whole spreadsheet, a particular worksheet, a row, or a column, and replace it with the string provided.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "replace", "sample_data": "replace"}, {"param_name": "search", "param_desc": "The string that needs to be replaced", "sample_data": "jack"}, {"param_name": "replace_with", "param_desc": "New replaced string", "sample_data": "jones"}, {"param_name": "scope", "param_desc": "workbook | worksheet | row | column", "sample_data": "workbook"}, {"param_name": "worksheet_name", "param_desc": "Required if the scope is either worksheet, or row, or column", "sample_data": ""}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Required if the scope is row", "sample_data": ""}, {"param_name": "column", "param_desc": "Required if the scope is column", "sample_data": ""}, {"param_name": "is_case_sensitive", "param_desc": "If set to true upper case and lower case characters will be different during search. By default it is false", "sample_data": "true"}, {"param_name": "is_exact_match", "param_desc": "If set to true the search with will match with the full content of the cell.", "sample_data": "true"}], "response": {"method": "replace", "status": "success"}}, "Recalculate": {"description": "This API can be used after a set of add or update records API call as the premimum APIs do not recalculate formula cells", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "10", "parameters": [{"param_name": "method", "param_desc": "recalculate", "sample_data": "recalculate"}], "response": {"status": "success"}}}, "updateworkbook": {"sub_list": ["Format ranges", "Image Fit Options", "Row Height", "<PERSON><PERSON><PERSON>", "Insert row", "Insert column", "Delete row", "Delete rows", "Delete column", "Set note to cell", "List all named ranges", "Create named range", "Update named range", "Delete named range"], "Format ranges": {"description": "This API can be used to add/edit text and cell-level formats of a range. <br/>Possible formats : <br/><li>1. \"bold/italic/underline/strikethrough/wrap_text\" : \"true/false\"</li> <li>2. \"font_color/fill_color\" : \"Hex Color Codes\"</li> <li>3. \"horizontal_alignment\" : \"start/center/end\"</li> <li>4. \"vertical_alignment\" : \"top/middle/bottom\"</li> <li>5. \"font_name\" : \"Arial/Calibri/Times New Roman/...\"</li><li>6. \"font_size/row_height/column_width\" : \"an Integer\"</li><li>7. \"date_time\" : \"(((dateformat1))) (or) (((dateformat2))) (or) (((dateformat3))) (or) (((dateformat4))) (or) (((datetimeformat1))) (or) (((datetimeformat2))) (or) (((datetimeformat3))) (or) (((datetimeformat4))) (or) (((datetimeformat5))) (or) (((timeformat1))) (or) (((timeformat2))) (or) (((timeformat3))) (or) (((timeformat4))) (or) (((timeformat5)))\"</li><li>8. \"border\" : {\"border_color\" :\"Hex Color Codes\", \"border_type\" :\"no_border / all_border /top_border / left_border / right_border / bottom_border / horizontal_border / vertical_border / outside_border / inside_border\"}. The parameter \"border_color\" is optional. The default border color is black.</li><li>9. \"currency/accounting\" : \"USD/EUR/INR/GBP/CNY/JPY/AUD/SGD/MYR/BRL\"</li><li>10. \"merge_cell\" : \"merge_range/merge_down/merge_across/merge_and_center\"</li>", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "is_domain_specific": true, "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "ranges.format.set", "sample_data": "ranges.format.set"}, {"param_name": "format_json", "param_type": "json_array", "param_desc": "an json array of format json objects. Example : [{\"worksheet_id\":\"1#\", \"range\":\"E2:E5\", \"fill_color\":\"#AACCEE\", \"date_time\":\"(((datetimeformat4)))\", \"border\": {\"border_color\":\"#77ADD6\", \"border_type\":\"all_border\"}}, {\"worksheet_id\":\"1#\", \"range\":\"A1:D4\", \"font_color\":\"#781647\", \"horizontal_alignment\":\"center\", \"bold\":true, \"font_size\":15, \"currency\":\"USD\"}]", "sample_data": [{"worksheet_id": "1#", "range": "A1:D4", "fill_color": "#AACCEE", "border": {"border_color": "#77ADD6", "border_type": "all_border"}}, {"worksheet_id": "1#", "range": "A1:D1", "font_color": "#781647", "horizontal_alignment": "center", "bold": true, "font_size": 15, "currency": "USD"}]}], "response": {"method": "ranges.format.set", "status": "success"}}, "Image Fit Options": {"description": "This API can be used to change the image fit option of all the cell images present in a range or a worksheet. This API will not have any effect on images over cells.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "range.images.fit", "sample_data": "range.images.fit"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose image options to be modified", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "start_row", "param_desc": "Start row index of the range whose image options to be modified", "sample_data": 2}, {"param_name": "start_column", "param_desc": "Start column index of the range whose image options to be modified", "sample_data": 1}, {"param_name": "end_row", "param_desc": "End row index of the range whose image options to be modified", "sample_data": 5}, {"param_name": "end_column", "param_desc": "End column index of the range whose image options to be modified", "sample_data": 2}, {"param_name": "image_fit_option", "param_desc": "fit | stretch | cover", "sample_data": "stretch"}], "response": {"method": "range.images.fit", "status": "success"}}, "Row Height": {"description": "This API can be used to set the height of multiple discontinuous rows.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.rows.height", "sample_data": "worksheet.rows.height"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose row height needs to be modified", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row_index_array", "param_type": "json_array", "param_desc": "Array of row indexes. For example : [{\"start_row\":4,\"end_row\":8},{\"start_row\":12,\"end_row\":12}].", "sample_data": [{"start_row": 4, "end_row": 8}, {"start_row": 12, "end_row": 12}]}, {"param_name": "auto_fit", "param_desc": "Optional parameter. If set to true, the height of the rows will be calculated based on cell data.", "sample_data": ""}, {"param_name": "row_height", "param_desc": "The height of the rows in pixels. Allowed value ranges from 1 to 2000. This parameter will not be considered if auto_fit is set to true.", "sample_data": "40"}], "response": {"method": "worksheet.rows.height", "status": "success"}}, "Column Width": {"description": "This API can be used to set the width of multiple discontinuous columns.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.columns.width", "sample_data": "worksheet.columns.width"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose column width needs to be modified", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "column_index_array", "param_type": "json_array", "param_desc": "Array of column indexes. For example : [{\"start_column\":4,\"end_column\":8},{\"start_column\":12,\"end_column\":12}].", "sample_data": [{"start_column": 4, "end_column": 8}, {"start_column": 12, "end_column": 12}]}, {"param_name": "auto_fit", "param_desc": "Optional parameter. If set to true, the width of the columns will be calculated based on cell data.", "sample_data": ""}, {"param_name": "column_width", "param_desc": "The width of the columns in pixels. Allowed value ranges from 1 to 2000. This parameter will not be considered if auto_fit is set to true.", "sample_data": "150"}], "response": {"method": "worksheet.columns.width", "status": "success"}}, "Insert row": {"description": "This API can be used to insert a new row into a specified worksheet. Cells of the newly inserted row will have the default formats.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "row.insert", "sample_data": "row.insert"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Row index at which new row to be inserted", "sample_data": 3}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284}]}, {"param_name": "header_row", "param_desc": "Optional parameter. By default, first row of the worksheet is considered as header row. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": ""}], "response": {"method": "row.insert", "status": "success"}}, "Insert column": {"description": "This API can be used to insert a new column into a specified worksheet. Cells of the newly inserted column will have the default formats.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "column.insert", "sample_data": "column.insert"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "column", "param_desc": "Column index after which new column to be inserted", "sample_data": 4}], "response": {"method": "column.insert", "status": "success"}}, "Delete row": {"description": "This API can be used to delete a single row of a worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "row.delete", "sample_data": "row.delete"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose row needs to be deleted", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Index position of the row which needs to be deleted", "sample_data": 4}], "response": {"method": "row.delete", "status": "success"}}, "Delete rows": {"description": "This API can be used to delete a multiple discontinuous rows of a worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "method", "param_desc": "worksheet.rows.delete", "sample_data": "worksheet.rows.delete"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose row needs to be deleted", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": "testworksheet"}, {"param_name": "row_index_array", "param_type": "json_array", "param_desc": "Array of row indexes. For example : [{\"start_row\":4,\"end_row\":8},{\"start_row\":12,\"end_row\":12}]. The rows from 4 to 8 and the row 12th will be deleted.", "sample_data": [{"start_row": 4, "end_row": 8}, {"start_row": 12, "end_row": 12}]}], "response": {"method": "worksheet.rows.delete", "status": "success"}}, "Delete column": {"description": "This API can be used to delete a single column of a worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "column.delete", "sample_data": "column.delete"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose column needs to be deleted", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "column", "param_desc": "Index position of the column which needs to be deleted", "sample_data": 2}], "response": {"method": "column.delete", "status": "success"}}, "Set note to cell": {"description": "This API can be used to create a note to a cell in a specified worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "120", "parameters": [{"param_name": "method", "param_desc": "cell.note.set", "sample_data": "cell.note.set"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "row", "param_desc": "Row index of the cell whose content needed an update", "sample_data": 3}, {"param_name": "column", "param_desc": "Column index of the cell whose content needed an update", "sample_data": 3}, {"param_name": "note", "param_desc": "The note that needed to be set", "sample_data": "examplenote"}], "response": {"method": "cell.note.set", "status": "success"}}, "List all named ranges": {"description": "This API can be used to get the list of all the named ranges in a specified spreadsheet.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "namedrange.list", "sample_data": "namedrange.list"}], "response": {"namedranges": [{"name_of_range": "abc", "worksheet_name": "Sheet1", "worksheet_id": "87656789#", "range": "A1:B4", "start_row": 1, "start_column": 1, "end_row": 4, "end_column": 2}, {"name_of_range": "de", "worksheet_name": "Sheet2", "worksheet_id": "33435644#", "range": "B8:C18", "start_row": 2, "start_column": 2, "end_row": 18, "end_column": 3}], "method": "namedrange.list", "status": "success"}}, "Create named range": {"description": "This API can be used to create an absolute named range. Named expressions are currently not supported via API.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "namedrange.create", "sample_data": "namedrange.create"}, {"param_name": "name_of_range", "param_desc": "Define name for the new named range", "sample_data": "example"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose range needs to be cleared", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "range", "param_desc": "The range that needs to be defined. For example A1:C8", "sample_data": "A1:C8"}], "response": {"method": "namedrange.create", "status": "success"}}, "Update named range": {"description": "This API can be used to update the source range of an existing named range.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "30", "parameters": [{"param_name": "method", "param_desc": "namedrange.update", "sample_data": "namedrange.update"}, {"param_name": "name_of_range", "param_desc": "Name of the range which needs to be modified", "sample_data": "example"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "range", "param_desc": "New range that need to be set. For example A1:D8", "sample_data": "A1:D8"}], "response": {"method": "namedrange.update", "status": "success"}}, "Delete named range": {"description": "This API can be used to delete an existing named range of a worksheet.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "namedrange.delete", "sample_data": "namedrange.delete"}, {"param_name": "name_of_range", "param_desc": "name of existing named range", "sample_data": "example"}], "response": {"method": "namedrange.delete", "status": "success"}}, "List all fields": {"description": "This API can be used to get the list of all the fields of a specified spreadsheet.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "60", "parameters": [{"param_name": "method", "param_desc": "field.list"}], "response": {"fields": [{"field_id": 1, "field_name": "abc"}, {"field_id": 2, "field_name": "def"}], "method": "field.list", "status": "success"}}, "Create field": {"description": "This API can be used to create a field.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "field.create"}, {"param_name": "field_names", "param_desc": "Array of field names. Eg. [\"Name\",\"Email\"]"}, {"param_name": "cells", "param_desc": "Array of discontinious ranges. For example:[{\"worksheet_id\" : \"0#\", \"row_index\" : 4, \"column_index\" : 3}, {\"worksheet_id\" : \"1#\", \"row_index\" : 1, \"column_index\" : 2}]"}], "response": {"method": "field.create", "status": "success"}}, "Update field": {"description": "This API can be used to edit a field.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "field.update"}, {"param_name": "field_name", "param_desc": "Define name for the new field"}, {"param_name": "cells", "param_desc": "Array of discontinious ranges. For example: [{\"worksheet_id\" : \"0#\", \"row_index\" : 4, \"column_index\" : 3}, {\"worksheet_id\" : \"1#\", \"row_index\" : 1, \"column_index\" : 2}]"}], "response": {"method": "field.update", "status": "success"}}, "Delete field": {"description": "This API can be used to delete an existing field of a workbook.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "20", "parameters": [{"param_name": "method", "param_desc": "field.delete"}, {"param_name": "field_name", "param_desc": "name of existing field"}], "response": {"method": "field.delete", "status": "success"}}}, "utility": {"sub_list": ["Convert Range to Index", "Convert Index to Range"], "Convert Range to Index": {"description": "This API can be used to convert range to start row, start column, end row, end column indexes respectively.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "120", "is_docs_api": true, "url": "utils", "parameters": [{"param_name": "method", "param_desc": "range.index.get", "sample_data": "range.index.get"}, {"param_name": "range_address", "param_desc": "The range to be converted to respective index. For example, A1:D3", "sample_data": "A1.D3"}], "response": {"method": "range.index.get", "start_row": 1, "start_column": 1, "end_row": 3, "end_column": 4, "status": "success"}}, "Convert Index to Range": {"description": "This API can be used to convert start row, start column, end row, end column indexes respectively to a Range.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "120", "is_docs_api": true, "url": "utils", "parameters": [{"param_name": "method", "param_desc": "range.address.get", "sample_data": "range.address.get"}, {"param_name": "start_row", "param_desc": "Start row index of the range", "sample_data": 1}, {"param_name": "start_column", "param_desc": "Start column index of the range", "sample_data": 1}, {"param_name": "end_row", "param_desc": "End row index of the range", "sample_data": 3}, {"param_name": "end_column", "param_desc": "End column index of the range", "sample_data": 4}], "response": {"method": "range.address.get", "range_address": "A1:D3", "status": "success"}}}, "premium": {"sub_list": ["Fetch records", "Add records", "Update records"], "Fetch records": {"intro": "The premium APIs are only available to the WorkDrive paid users or Zoho One users. These APIs have lot higher threshold limit and useful for high speed operation.", "description": "Fetch records API is used to fetch records of a worksheet after filtering the records based on some criteria. If no criteria is mentioned it will return all the records. A maximum of 200 records can be fetched using one API call.<br/><br/>The criteria_json should be in the following format. <br/>[{\"key\":\"&lt;Column header name&gt;\",\"operator\":\"&lt;operator&gt;\",\"matcher\":\"&lt;Matched String&gt;\",\"type\":\"String | Date | Number\"}]<br/>If there are more than 1 criteria, criteria_pattern should be used.<br/><br/>Supported operators are : EQUALS | NOT_EQUALS | CONTAINS | NOT_CONTAINS | GREATER_THAN | LESS_THAN | IS_EMPTY | IS_NOT_EMPTY | IN | NOT_IN<br/>The key \"type\" is optional and the default value is String. Supported types are: String | Date | Number.<br/>Please note that only EQUALS, NOT_EQUALS, GREATER_THAN, and LESS_THAN are supported for date and number type data. CONTAINS, NOT_CONTAINS, IN, and NOT_IN works for String type data only.<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria_json : <br/><ul style=\"list-style-type: none;\"><li>1. [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"}]</li><li>2.criteria_json = [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}] <br/> criteria_pattern = 1 AND 2<li>3.criteria_json = [{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pen\"},{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pencil\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":40,\"type\":\"Number\"}] <br/> criteria_pattern = (1 OR 2) AND 3</li></ul>Maximum of 5 criteria can be used.", "scope": "ZohoSheet.dataAPI.READ", "usage_limit": "500", "is_docs_api": true, "url": "fetchrecords", "parameters": [{"param_name": "resource_id", "param_desc": "Resource ID of the Zoho Sheet document", "sample_data": "***"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet whose records needs to be fetched", "sample_data": "testworksheet"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. By default, first row of the worksheet is considered as header row. This can be used if tabular data starts from any row other than the first row.", "sample_data": 1}, {"param_name": "criteria_json", "param_desc": "Optional parameter. Can be used to filter records.", "sample_data": "[{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\",\"type\":\"String\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}]"}, {"param_name": "criteria_pattern", "param_desc": "Required when more than 1 criteria is available under criteria_json", "sample_data": "1 AND 2"}, {"param_name": "column_names", "param_desc": "Optional parameter. Can be used to read particular column's data. By default all the column data will be available in response. Multiple column names must be separated by comma.", "sample_data": "Month,Amount"}, {"param_name": "render_option", "param_desc": "Optional parameter. It defines how the value should be rendered. Possible options are formatted, unformatted, and formula.", "sample_data": "formatted"}, {"param_name": "records_start_index", "param_desc": "Optional parameter. This parameter can be used to get a few resources if there are too many.", "sample_data": 1}, {"param_name": "count", "param_desc": "Optional parameter. It denotes the number of records.", "sample_data": 2}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}], "response": {"records": [{"Month": "March", "Source": "<PERSON>", "Item": "Pen", "Amount": 60, "row_index": 1}, {"Month": "April", "Source": "<PERSON>", "Item": "Pencil", "Amount": 40, "row_index": 2}], "records_count": 21, "records_start_index": 1, "records_end_index": 3, "status": "success"}}, "Add records": {"description": "This API can be used to add records to a worksheet. The records will be in JSON formatted data and will be appended after the last used row of the worksheet. The key of JSON Object will represent the column header of the worksheet. This API is useful for high speed data collection. Only 1 row can be added using a single API call.<br/><br/>Please note that, this API will not recalculate any formula cell or it will not update any pivot table to support higher threshold. If you have any such requirements, please use <u><a onclick='document.getElementById(\"tabularOperation\").click();document.getElementById(\"tabular\").children[1].click();'>Add records to worksheet API</a></u>. You can also use Recalculate API (available under Content APIs) after completion of all operations.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "500", "is_docs_api": true, "url": "addrecords", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "resource_id", "param_desc": "Resource ID of the Zoho Sheet document", "sample_data": "***"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. Default value is 1. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": 1}, {"param_name": "json_data", "param_type": "json_array", "param_desc": "JSON Array. Example : [{\"Name\":\"<PERSON>\",\"Region\":\"South\",\"Units\":284}]. \"Name\", \"Region\", and \"Units\" are the table headers.", "sample_data": [{"Name": "<PERSON>", "Region": "South", "Units": 284}]}], "response": {"status": "success", "sheet_name": "Sheet1", "start_row": 10, "start_column": 1, "end_row": 11, "end_column": 3}}, "Update records": {"description": "This API is used to update records based on some criteria or based on a row index. The API is useful for updating few rows (maximum of 10) with higher threshold. <br/><br/>Please note that, this API will not recalculate any formula cell or it will not update any pivot table. The number of rows that can be updated is also limited to support higher threshold. If a large number of rows that can get updated for a particular criteria or if you have requirement to recalculate formula cells, please use <u><a onclick='document.getElementById(\"tabularOperation\").click();document.getElementById(\"tabular\").children[2].click();'>Update records in worksheet API</a></u>. You can also use Recalculate API (available under Content APIs) after completion of all operations.<br/><br/>The criteria_json should be in the following format. <br/>[{\"key\":\"&lt;Column header name&gt;\",\"operator\":\"&lt;operator&gt;\",\"matcher\":\"&lt;Matched String&gt;\",\"type\":\"String | Date | Number\"}]<br/>If there are more than 1 criteria, criteria_pattern should be used.<br/><br/>Supported operators are : EQUALS | NOT_EQUALS | CONTAINS | NOT_CONTAINS | GREATER_THAN | LESS_THAN | IS_EMPTY | IS_NOT_EMPTY | IN | NOT_IN<br/>The key \"type\" is optional and the default value is String. Supported types are: String | Date | Number.<br/>Please note that only EQUALS, NOT_EQUALS, GREATER_THAN, and LESS_THAN are supported for date and number type data. CONTAINS, NOT_CONTAINS, IN, and NOT_IN works for String type data only.<div class=\"tabular_table_example\">Consider the following table :<br/><table class=\"exampleTable\"><tr style=\"text-align: left;\"><th>Month</th><th>Source</th><th>Item</th><th>Amount</th></tr><tr><td>March</td><td>Anderson</td><td>Pen</td><td>60</td></tr><tr><td>April</td><td>Gill</td><td>Pencil</td><td>40</td></tr><tr><td>April</td><td>Gill</td><td>Pen Stand</td><td>42</td></tr></table></div>Example criteria_json : <br/><ul style=\"list-style-type: none;\"><li>1. [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"}]</li><li>2.criteria_json = [{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}] <br/> criteria_pattern = 1 AND 2<li>3.criteria_json = [{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pen\"},{\"key\":\"Item\",\"operator\":\"EQUALS\",\"matcher\":\"Pencil\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":40,\"type\":\"Number\"}] <br/> criteria_pattern = (1 OR 2) AND 3</li></ul>Maximum of 5 criteria can be used.", "scope": "ZohoSheet.dataAPI.UPDATE", "usage_limit": "500", "is_docs_api": true, "url": "updaterecords", "import_needed": true, "import_data": [{"java": "import net.sf.json.JSONArray;\nimport net.sf.json.JSONObject;", "python": "<span class=\"hlString\">import</span> json\n"}], "parameters": [{"param_name": "resource_id", "param_desc": "Resource ID of the Zoho Sheet document", "sample_data": "***"}, {"param_name": "worksheet_name", "param_desc": "Name of the worksheet", "sample_data": "Sheet1"}, {"param_name": "worksheet_id", "param_desc": "Alternatively worksheet_id can be used instead of worksheet_name", "sample_data": ""}, {"param_name": "header_row", "param_desc": "Optional parameter. Default value is 1. This can be mentioned if the table header is not in the first row of the worksheet.", "sample_data": 1}, {"param_name": "criteria_json", "param_desc": "Optional parameter. Can be used to filter records.", "sample_data": "[{\"key\":\"Month\",\"operator\":\"EQUALS\",\"matcher\":\"March\",\"type\":\"String\"},{\"key\":\"Amount\",\"operator\":\"GREATER_THAN\",\"matcher\":50,\"type\":\"Number\"}]"}, {"param_name": "criteria_pattern", "param_desc": "Required when more than 1 criteria is available under criteria_json", "sample_data": "1 AND 2"}, {"param_name": "row_index", "param_desc": "Optional parameter. Either criteria or row index is required for processing.", "sample_data": 1}, {"param_name": "first_match_only", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}, {"param_name": "is_case_sensitive", "param_desc": "Optional parameter. By default it is true. Can be set as false for case insensitive search.", "sample_data": true}, {"param_name": "data", "param_type": "json_object", "param_desc": "The JSON data that needs to be updated. Example:{\"Month\":\"May\",\"Amount\":50}", "sample_data": {"Month": "May", "Amount": 50}}], "response": {"no_of_affected_rows": 2, "status": "success"}}}}